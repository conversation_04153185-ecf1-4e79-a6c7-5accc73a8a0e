package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.dto.param.KnowledgeParam;
import cn.iocoder.yudao.aiBase.dto.request.FileUploadRequest;
import cn.iocoder.yudao.aiBase.dto.response.KnowledgeResponse;
import cn.iocoder.yudao.aiBase.service.AiAppKnowledgeFileService;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;

/**
 * 用户知识库
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ai-base/knowledge")
@Tag(name = "知识库")
public class KnowledgeController {

    @Autowired
    private AiAppKnowledgeFileService aiAppKnowledgeFileService;

    /**
     * 知识库列表
     * @return
     */
    @PostMapping("/getKnowledgeByDir")
    @Operation(summary = "知识库列表")
    public CommonResult<List<KnowledgeResponse>> getKnowledgeByDir(@Valid @RequestBody KnowledgeParam reqVO, @RequestHeader(value= HttpHeaders.AUTHORIZATION) String auth) {
        try {
            return CommonResult.success(aiAppKnowledgeFileService.selectList(auth, reqVO));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/create")
    @Operation(summary = "创建知识库")
    public CommonResult<KnowledgeResponse> create(@Valid @RequestBody FileUploadRequest reqVO, @RequestHeader(value= HttpHeaders.AUTHORIZATION) String auth) {
        try {
            return CommonResult.success(aiAppKnowledgeFileService.create(auth, reqVO));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/updateFileName")
    @Operation(summary = "更新文件名")
    public CommonResult<Boolean> updateFileName(@Valid @RequestBody KnowledgeParam reqVO, @RequestHeader(value= HttpHeaders.AUTHORIZATION) String auth) {
        try {
            return CommonResult.success(aiAppKnowledgeFileService.updateFileName(auth, reqVO.getId(), reqVO.getFileName()));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/getFilePath")
    @Operation(summary = "获取文件路径")
    public CommonResult<List<KnowledgeResponse>> getFilePath(@RequestParam("id") Integer id, @RequestHeader(value= HttpHeaders.AUTHORIZATION) String auth) {
        try {
            return CommonResult.success(aiAppKnowledgeFileService.getFilePath(auth, id));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/delete")
    @Operation(summary = "删除知识库")
    public CommonResult<Boolean> delete(@RequestParam("id") Integer id, @RequestHeader(value= HttpHeaders.AUTHORIZATION) String auth) {
        try {
            return CommonResult.success(aiAppKnowledgeFileService.delete(auth, id));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }



}
