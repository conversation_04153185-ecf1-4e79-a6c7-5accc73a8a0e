package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.dto.request.dify.WorkflowsRunRequest;
import cn.iocoder.yudao.aiBase.service.ApiTokensService;
import cn.iocoder.yudao.aiBase.service.OpenApiService;
import cn.iocoder.yudao.aiBase.util.DifyUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;

/**
 * OpenApi通用转发接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/ai-base/openapi")
@Tag(name = "OpenApi通用转发接口")
@PermitAll
public class OpenApiController {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ApiTokensService apiTokensService;

    @Autowired
    private OpenApiService openApiService;
    
    @PostConstruct
    public void init() {
        // 配置RestTemplate
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        // 设置连接超时为180秒
        factory.setConnectTimeout(180000);
        // 不使用setReadTimeout，因为在当前版本不可用
        restTemplate.setRequestFactory(factory);
        restTemplate.setErrorHandler(new DefaultResponseErrorHandler());
    }
    
    /**
     * OpenApi通用转发接口
     * 
     * @param request HTTP请求
     * @return 响应结果
     */
    @RequestMapping(value = "/**", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    @Operation(summary = "OpenApi通用转发接口")
    @PermitAll
    public Object proxyRequest(
                             HttpServletRequest request) {
        try {
            // 获取请求路径
            String requestUri = request.getRequestURI();
            String contextPath = "/ai-base/openapi";
            String path = requestUri.substring(requestUri.indexOf(contextPath) + contextPath.length());

            // 确保path以/开头
            if (!path.startsWith("/")) {
                path = "/" + path;
            }

            return openApiService.proxyRequest(request, path);
        } catch (Exception e) {
            log.error("处理请求时发生错误", e);
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), "处理请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 意见反馈透传接口
     * 将请求完全透传到配置的意见反馈服务
     *
     * @param request HTTP请求
     * @return 响应结果
     */
    @PostMapping(value = "/feedback")
    @Operation(summary = "意见反馈透传接口")
    @PermitAll
    public Object feedbackProxy(HttpServletRequest request) {
        return openApiService.proxyToTarget("feedback", request);
    }

    /**
     * 留言反馈透传接口
     * 将请求完全透传到配置的留言反馈服务
     *
     * @param request HTTP请求
     * @return 响应结果
     */
    @PostMapping(value = "/leave-message")
    @Operation(summary = "留言反馈透传接口")
    @PermitAll
    public Object leaveMessageProxy(HttpServletRequest request) {
        return openApiService.proxyToTarget("leave-message", request);
    }

    /**
     * 图片上传透传接口
     * 将请求完全透传到配置的图片上传服务
     *
     * @param request HTTP请求
     * @return 响应结果
     */
    @PostMapping(value = "/upload-picture")
    @Operation(summary = "图片上传透传接口")
    @PermitAll
    public Object uploadPictureProxy(HttpServletRequest request) {
        return openApiService.proxyToTarget("upload-picture", request);
    }

    /**
     * 执行workflow
     * @param param
     * @return
     */
    @PostMapping(value = "/workflows_run")
    @Operation(summary = "执行workflow")
    public Object workflowsRun(@Valid @RequestBody WorkflowsRunRequest param) {
        if (DifyUtil.STREAMING_MODE.equals(param.getResponse_mode())) {
            // 使用一个线程或反应式流来订阅第三方SSE事件并转发到前端
            Flux<ServerSentEvent> flux = apiTokensService.workflowsRun(param);
            SseEmitter emitter = apiTokensService.getEmitter(flux);
            return emitter;
        } else {
            try {
                JSONObject res = apiTokensService.workflowsRun1(param);
                return CommonResult.success(res);
            } catch (Exception e) {
                return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
            }
        }
    }
}