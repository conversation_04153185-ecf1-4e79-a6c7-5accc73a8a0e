package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * AI应用知识文件实体类
 */
@TableName("ai_app_knowledge_file")
@KeySequence("ai_app_knowledge_file_id_seq")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiAppKnowledgeFile extends Model<AiAppKnowledgeFile> {

    @Schema(description = "主键")
    @TableId
    private Integer id;

    @Schema(description = "三方用户ID")
    private Long socialUserId;

    @Schema(description = "三方类型")
    private Integer socialType;

    @Schema(description = "父级ID")
    private Integer pid;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "COS地址")
    private String cosFilePath;

    @Schema(description = "CDN签名地址")
    private String cdnSignedUrl;

    @Schema(description = "文件大小")
    private Long fileSize;

    @Schema(description = "文件MD5值")
    private String fileMd5;

    @Schema(description = "签名过期时间")
    private Long t;

    @Schema(description = "关联Dify信息")
    private String difyInfo;

    @Schema(description = "1目录，2文件")
    private Integer type;

    @Schema(description = "层级")
    private Integer level;

    @Schema(description =  "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdAt;

    @Schema(description =  "更新时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime updatedAt;

    @Schema(description =  "0正常 1删除")
    @TableLogic
    private Integer deleted;
}
