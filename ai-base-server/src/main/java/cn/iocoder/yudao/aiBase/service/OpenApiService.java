package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.ProxyConfig;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.IOException;
import java.net.URI;
import java.util.Enumeration;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;

@Slf4j
@Service
public class OpenApiService {

    @Value("${dify-base.openapi-host}")
    private String openapiHost;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ProxyConfig proxyConfig;

    public Object proxyRequest(HttpServletRequest request, String path) throws IOException {
            // 构建目标URL - 确保没有双斜杠
            String baseUrl = openapiHost;
            if (baseUrl.endsWith("/")) {
                baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
            }
            String targetUrl = baseUrl + path;

            // 添加查询参数
            String queryString = request.getQueryString();
            if (queryString != null && !queryString.isEmpty()) {
                targetUrl = targetUrl + "?" + queryString;
            }

            URI uri = URI.create(targetUrl);

            // 复制请求头
            HttpHeaders headers = new HttpHeaders();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                // 过滤掉一些不需要转发的请求头
                if (!headerName.equalsIgnoreCase("host") && !headerName.equalsIgnoreCase("content-length")) {
                    headers.set(headerName, request.getHeader(headerName));
                }
            }

            // 记录请求头信息
            if (log.isDebugEnabled()) {
                StringBuilder headerLog = new StringBuilder("请求头信息:\n");
                headers.forEach((name, values) -> {
                    headerLog.append(name).append(": ").append(values).append("\n");
                });
                log.debug(headerLog.toString());
            }

            // 获取Content-Type
            String contentType = request.getContentType();

            // 创建请求实体
            HttpEntity<?> httpEntity;

            // 根据Content-Type处理不同类型的请求
            if (contentType != null) {
                // 处理表单数据 (application/x-www-form-urlencoded)
                if (contentType.contains(MediaType.APPLICATION_FORM_URLENCODED_VALUE)) {
                    MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
                    // 获取所有表单参数
                    Map<String, String[]> parameterMap = request.getParameterMap();
                    if (parameterMap != null) {
                        parameterMap.forEach((key, values) -> {
                            for (String value : values) {
                                formData.add(key, value);
                            }
                        });
                    }
                    log.info("处理表单数据 (application/x-www-form-urlencoded): {}", formData);
                    httpEntity = new HttpEntity<>(formData, headers);
                }
                // 处理文件上传 (multipart/form-data)
                else if (contentType.contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
                    if (request instanceof MultipartHttpServletRequest) {
                        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
                        MultiValueMap<String, Object> parts = new LinkedMultiValueMap<>();

                        // 添加普通表单字段
                        multipartRequest.getParameterMap().forEach((key, values) -> {
                            for (String value : values) {
                                parts.add(key, value);
                            }
                        });

                        // 添加文件字段
                        multipartRequest.getFileMap().forEach((name, file) -> {
                            parts.add(name, file.getResource());
                        });

                        httpEntity = new HttpEntity<>(parts, headers);
                    } else {
                        // 如果不能处理为MultipartHttpServletRequest，则使用通用方法
                        byte[] body = StreamUtils.copyToByteArray(request.getInputStream());
                        httpEntity = new HttpEntity<>(body, headers);
                    }
                }
                // 处理JSON和其他类型
                else {
                    byte[] body = StreamUtils.copyToByteArray(request.getInputStream());
                    httpEntity = new HttpEntity<>(body, headers);
                }
            } else {
                // 没有Content-Type的情况
                if (request.getContentLength() > 0) {
                    byte[] body = StreamUtils.copyToByteArray(request.getInputStream());
                    httpEntity = new HttpEntity<>(body, headers);
                } else {
                    httpEntity = new HttpEntity<>(headers);
                }
            }

            // 获取请求方法
            String requestMethod = request.getMethod();
            HttpMethod method;
            if ("GET".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.GET;
            } else if ("POST".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.POST;
            } else if ("PUT".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.PUT;
            } else if ("DELETE".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.DELETE;
            } else if ("HEAD".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.HEAD;
            } else if ("OPTIONS".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.OPTIONS;
            } else if ("PATCH".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.PATCH;
            } else if ("TRACE".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.TRACE;
            } else {
                method = HttpMethod.GET;
            }

            // 发送请求
            try {
                log.info("转发请求到: {}, 方法: {}, Content-Type: {}", targetUrl, method, contentType);
                ResponseEntity<Object> responseEntity = restTemplate.exchange(uri, method, httpEntity, Object.class);
                log.info("转发请求成功，状态码: {}", responseEntity.getStatusCode());
                return responseEntity.getBody();
            } catch (Exception e) {
                log.error("OpenApi通用转发接口失败，目标URL: {}，错误: {}", targetUrl, e.getMessage(), e);

                // 提取并返回更详细的错误信息
                String errorMessage = e.getMessage();
                if (e instanceof org.springframework.web.client.HttpStatusCodeException) {
                    org.springframework.web.client.HttpStatusCodeException httpError =
                            (org.springframework.web.client.HttpStatusCodeException) e;
                    errorMessage = "目标服务器返回错误 " + httpError.getStatusCode() + ": " +
                            httpError.getResponseBodyAsString();
                }

                return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), errorMessage);
            }
    }

    /**
     * 通用代理透传方法
     *
     * @param targetName 目标名称
     * @param request HTTP请求
     * @return 响应结果
     */
    public Object proxyToTarget(String targetName, HttpServletRequest request) {
        try {
            // 获取代理目标配置
            ProxyConfig.ProxyTarget target = proxyConfig.getTarget(targetName);
            if (target == null) {
                log.error("代理目标配置不存在: {}", targetName);
                return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), "代理目标配置不存在: " + targetName);
            }

            String targetUrl = target.getUrl();
            log.info("开始透传请求到目标: {}, URL: {}", targetName, targetUrl);

            // 构建目标URI
            URI uri = URI.create(targetUrl);

            // 获取原始Content-Type
            String originalContentType = request.getContentType();
            log.info("原始Content-Type: {}", originalContentType);

            // 复制请求头
            HttpHeaders headers = new HttpHeaders();
            if (target.isForwardHeaders()) {
                Enumeration<String> headerNames = request.getHeaderNames();
                while (headerNames.hasMoreElements()) {
                    String headerName = headerNames.nextElement();
                    // 过滤掉一些不需要转发的请求头
                    if (!headerName.equalsIgnoreCase("host") &&
                            !headerName.equalsIgnoreCase("content-length") &&
                            !headerName.equalsIgnoreCase("connection")) {

                        String headerValue = request.getHeader(headerName);
                        headers.set(headerName, headerValue);

                        // 记录重要请求头的转发
                        if (headerName.equalsIgnoreCase("cookie")) {
                            log.info("转发Cookie头: {}", headerValue);
                        } else if (headerName.equalsIgnoreCase("content-type")) {
                            log.info("转发Content-Type头: {}", headerValue);
                        }
                    }
                }
            }

            // 根据配置决定是否强制设置Content-Type为application/json
            if (target.isForceJsonContentType()) {
                headers.setContentType(MediaType.APPLICATION_JSON);
                log.info("强制设置Content-Type为application/json");
            } else {
                log.info("保持原始Content-Type: {}", originalContentType);
            }

            // 创建请求实体 - 根据Content-Type处理不同类型的请求
            HttpEntity<?> httpEntity;

            if (originalContentType != null) {
                // 处理表单数据 (application/x-www-form-urlencoded)
                if (originalContentType.contains(MediaType.APPLICATION_FORM_URLENCODED_VALUE)) {
                    MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
                    // 获取所有表单参数
                    Map<String, String[]> parameterMap = request.getParameterMap();
                    if (parameterMap != null) {
                        parameterMap.forEach((key, values) -> {
                            for (String value : values) {
                                formData.add(key, value);
                            }
                        });
                    }
                    log.info("处理表单数据 (application/x-www-form-urlencoded): {}", formData);
                    httpEntity = new HttpEntity<>(formData, headers);
                }
                // 处理文件上传 (multipart/form-data)
                else if (originalContentType.contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
                    log.info("处理Multipart请求");
                    if (request instanceof MultipartHttpServletRequest) {
                        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
                        MultiValueMap<String, Object> parts = new LinkedMultiValueMap<>();

                        // 添加普通表单字段
                        multipartRequest.getParameterMap().forEach((key, values) -> {
                            for (String value : values) {
                                parts.add(key, value);
                                log.debug("添加表单字段: {} = {}", key, value);
                            }
                        });

                        // 添加文件字段
                        multipartRequest.getFileMap().forEach((name, file) -> {
                            parts.add(name, file.getResource());
                            log.debug("添加文件字段: {} = {} (大小: {} bytes)", name, file.getOriginalFilename(), file.getSize());
                        });

                        log.info("Multipart parts总数: {}", parts.size());
                        httpEntity = new HttpEntity<>(parts, headers);
                    } else {
                        // 如果不能处理为MultipartHttpServletRequest，则使用通用方法
                        log.warn("无法解析为MultipartHttpServletRequest，使用字节流方式");
                        byte[] body = StreamUtils.copyToByteArray(request.getInputStream());
                        log.debug("请求体大小: {} bytes", body.length);
                        httpEntity = new HttpEntity<>(body, headers);
                    }
                }
                // 处理JSON和其他类型
                else {
                    byte[] body = StreamUtils.copyToByteArray(request.getInputStream());
                    String requestBody = new String(body);
                    log.debug("请求体内容: {}", requestBody);
                    httpEntity = new HttpEntity<>(body, headers);
                }
            } else {
                // 没有Content-Type的情况
                if (request.getContentLength() > 0) {
                    byte[] body = StreamUtils.copyToByteArray(request.getInputStream());
                    log.debug("无Content-Type，请求体大小: {} bytes", body.length);
                    httpEntity = new HttpEntity<>(body, headers);
                } else {
                    log.debug("无Content-Type且无请求体");
                    httpEntity = new HttpEntity<>(headers);
                }
            }

            // 记录最终的请求头信息
            log.debug("最终请求头: {}", headers);

            // 确定HTTP方法
            HttpMethod method = HttpMethod.valueOf(target.getMethod().toUpperCase());

            // 发送请求
            log.info("发送透传请求: {} {} (Content-Type: {})", method, targetUrl, originalContentType);
            ResponseEntity<Object> responseEntity = restTemplate.exchange(uri, method, httpEntity, Object.class);

            log.info("透传请求成功，状态码: {}", responseEntity.getStatusCode());

            // 返回响应体
            return responseEntity.getBody();

        } catch (Exception e) {
            log.error("透传请求失败，目标: {}, 错误: {}", targetName, e.getMessage(), e);

            // 提取并返回更详细的错误信息
            String errorMessage = e.getMessage();
            if (e instanceof org.springframework.web.client.HttpStatusCodeException) {
                org.springframework.web.client.HttpStatusCodeException httpError =
                        (org.springframework.web.client.HttpStatusCodeException) e;
                errorMessage = "目标服务器返回错误 " + httpError.getStatusCode() + ": " +
                        httpError.getResponseBodyAsString();
            }

            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), errorMessage);
        }
    }


}
