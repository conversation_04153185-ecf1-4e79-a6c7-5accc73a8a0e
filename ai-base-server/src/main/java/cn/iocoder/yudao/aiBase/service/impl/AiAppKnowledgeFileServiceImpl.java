package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.ErrorCodeConstants;
import cn.iocoder.yudao.aiBase.dto.request.FileUploadRequest;
import cn.iocoder.yudao.aiBase.dto.response.KnowledgeResponse;
import cn.iocoder.yudao.aiBase.entity.AiAppKnowledgeFile;
import cn.iocoder.yudao.aiBase.mapper.AiAppKnowledgeFileMapper;
import cn.iocoder.yudao.aiBase.service.AiAppKnowledgeFileService;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.param.KnowledgeParam;
import cn.iocoder.yudao.aiBase.service.OauthService;
import cn.iocoder.yudao.aiBase.util.CommonUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
@DS(DBConstant.AiBase)
public class AiAppKnowledgeFileServiceImpl extends ServiceImpl<AiAppKnowledgeFileMapper, AiAppKnowledgeFile> implements AiAppKnowledgeFileService {

    @Autowired
    private OauthService oauthService;

    @Override
    public List<AiAppKnowledgeFile> selectList(KnowledgeParam reqVO) {
        return baseMapper.selectList(reqVO);
    }

    @Override
    public List<KnowledgeResponse> selectList(String auth, KnowledgeParam reqVO) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);
        reqVO.setSocialUserId(authUser.getUserId());
        reqVO.setSocialType(authUser.getUserType());

        List<AiAppKnowledgeFile> files = selectList(reqVO);
        List<KnowledgeResponse> res = new ArrayList<>();
        for (AiAppKnowledgeFile file : files) {
            res.add(toBean(file));
        }
        return res;
    }

    public void updateCdn(KnowledgeResponse file) {
        Long time = System.currentTimeMillis()/1000 - BaseConstant.SIX;
        if (BaseConstant.TWO.equals(file.getType()) && time > file.getT()) {
            // todo 更新cdn签名地址
            file.setCdnSignedUrl("");
        }
    }

    @Override
    public Boolean updateFileName(String auth, Integer id, String fileName) {
        if (id == null || StringUtils.isEmpty(fileName)) {
            throw exception(ErrorCodeConstants.BAD_REQUEST);
        }
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);
        AiAppKnowledgeFile file = getById(id);
        if (file == null) {
            return true;
        }

        if (file.getSocialUserId().equals(authUser.getUserId()) && file.getSocialType().equals(authUser.getUserType())) {
            file.setFileName(fileName);
            file.setUpdatedAt(LocalDateTime.now());
            return updateById(file);
        }

        throw exception(ErrorCodeConstants.FORBIDDEN);
    }

    @Override
    public KnowledgeResponse create(String auth, FileUploadRequest reqVO) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);
        Integer level = 0;
        if (reqVO.getPid() > 0) {
            AiAppKnowledgeFile parent = getById(reqVO.getPid());
            if (parent == null || BaseConstant.TWO.equals(parent.getType()) ||
                    !parent.getSocialUserId().equals(authUser.getUserId()) || !parent.getSocialType().equals(authUser.getUserType())) {
                throw exception(ErrorCodeConstants.FORBIDDEN);
            }
            level = parent.getLevel() + 1;
            if (level >= BaseConstant.TEN) {
                throw exception(ErrorCodeConstants.ERROR_5056);
            }
        }

        AiAppKnowledgeFile file = new AiAppKnowledgeFile();
        file.setPid(reqVO.getPid());
        file.setType(reqVO.getType());
        file.setFileName(reqVO.getFileName());
        file.setSocialUserId(authUser.getUserId());
        file.setSocialType(authUser.getUserType());
        file.setLevel(level);
        file.setCreatedAt(LocalDateTime.now());
        file.setUpdatedAt(LocalDateTime.now());
        save(file);

//        HttpServletRequest request
//        proxyRequest(HttpServletRequest request, String path)

        return toBean(file);
    }

    @Override
    public List<KnowledgeResponse> getFilePath(String auth, Integer id) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);
        AiAppKnowledgeFile file = getById(id);
        if (file == null || !file.getSocialUserId().equals(authUser.getUserId()) || !file.getSocialType().equals(authUser.getUserType())) {
            throw exception(ErrorCodeConstants.FORBIDDEN);
        }

        List<KnowledgeResponse> res = new ArrayList<>();
        res.add(toBean(file));
        // 递归查询所有父节点
        recursiveSelectParents(res, file.getPid());

        return res.stream().sorted((o1, o2) -> o1.getLevel().compareTo(o2.getLevel())).collect(Collectors.toList());
    }

    /**
     * 递归查询父节点
     *
     * @param res 结果列表
     * @param pid 父节点ID
     */
    private void recursiveSelectParents(List<KnowledgeResponse> res, Integer pid) {
        // 查询父节点
        if (pid != null && pid > BaseConstant.ZERO) {
            AiAppKnowledgeFile parent = getById(pid);
            if (parent != null) {
                res.add(toBean(parent));
                // 继续递归查询父节点的父节点
                recursiveSelectParents(res, parent.getPid());
            }
        }
    }

    @Override
    public Boolean delete(String auth, Integer id) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);
        AiAppKnowledgeFile file = getById(id);
        if (file == null) {
            return true;
        }

        if (file.getSocialUserId().equals(authUser.getUserId()) && file.getSocialType().equals(authUser.getUserType())) {
            return removeById(id);
        }

        throw exception(ErrorCodeConstants.FORBIDDEN);
    }

    public KnowledgeResponse toBean(AiAppKnowledgeFile file) {
        return BeanUtils.toBean(file, KnowledgeResponse.class, item -> {
            updateCdn(item);
            item.setCreatedAt(file.getCreatedAt().format(CommonUtil.DateTimeFormat2));
        });
    }

}