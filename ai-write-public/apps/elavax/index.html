<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>ElavaX AI - 智能评估优化平台</title>
    <meta
      name="description"
      content="ElavaX利用AI对您的研究构想进行全面评估，提供深度修改建议，助您精准匹配目标，实现卓越。立即体验，优化您的每一个创见！。"
    />
    <meta name="keywords" content="AI评估平台,智能评估工具,研究构想评估,项目评估AI,智能优化平台,AI分析评估系统" />
    <script src="./tailwindcss.js"></script>
    <link href="./css/font-awesome.min.css" rel="stylesheet" />
    <script>
      tailwind.config = {
        theme: {
          screens: {
            'sm': {'max': '639px'},
            'md': '768px',
            'lg': '1024px',
            'xl': '1280px',
            '2xl': '1536px',
          },
          extend: {
            colors: {
              dark: "#121212",
              "dark-blue": "#161B33",
              "deep-blue": "#2C3E50",
              accent: "#3498DB",
              glow: "#4A90E2",
              primary: '#165DFF',
            secondary: '#4080FF',
            light: '#F2F3F5',
            'light-border': '#C9CDD4',
            success: '#00B42A',
            error: '#F53F3F',
            warning: '#FF7D00',
            info: '#86909C'
            },
            fontFamily: {
              inter: ["Inter", "sans-serif"],
            },
            animation: {
              float: "float 6s ease-in-out infinite",
              "pulse-slow": "pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite",
              glow: "glow 2s ease-in-out infinite alternate",
            },
            keyframes: {
              float: {
                "0%, 100%": { transform: "translateY(0)" },
                "50%": { transform: "translateY(-10px)" },
              },
              glow: {
                "0%": { boxShadow: "0 0 5px rgba(74, 144, 226, 0.3)" },
                "100%": { boxShadow: "0 0 20px rgba(74, 144, 226, 0.6)" },
              },
            },
          },
        },
      };
    </script>
    <style type="text/tailwindcss">
      @layer utilities {
        .content-auto {
          content-visibility: auto;
        }
        .text-shadow-glow {
          text-shadow: 0 0 10px rgba(74, 144, 226, 0.5);
        }
        .gradient-border-75 {
          position: relative;
        }
        .gradient-border-75::before {
          content: "";
          position: absolute;
          border-radius: 24px;
          background: linear-gradient(60deg, #ffffff6a, #ffffff1a, #ffffff6a);
          -webkit-mask: linear-gradient(#fff 0 0) content-box,
            linear-gradient(#fff 0 0);
          -webkit-mask-composite: xor;
          mask-composite: exclude;
          pointer-events: none;
          border-radius: 0.75rem;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
        }
        .card-hover {
          transition: all 0.3s ease;
        }
        .card-hover:hover {
          transform: translateY(-10px);
          box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        }
        .node {
          border-radius: 50%;
          background: radial-gradient(
            circle at 30% 30%,
            rgba(74, 144, 226, 0.8),
            rgba(74, 144, 226, 0.1)
          );
          filter: blur(5px);
        }
        .grid-line {
          background-color: rgba(255, 255, 255, 0.05);
        }
        .copy-btn {
          transition: all 0.3s ease;
        }

        .copy-btn:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .copy-btn:active {
          transform: scale(0.95);
        }

        .copied {
          animation: copied 2s ease-in-out;
        }

        @keyframes copied {
          0% {
            opacity: 0;

            transform: translateY(10px);
          }

          20% {
            opacity: 1;

            transform: translateY(0);
          }

          80% {
            opacity: 1;

            transform: translateY(0);
          }

          100% {
            opacity: 0;

            transform: translateY(-10px);
          }
        }

        .fa-envelope-o-stroke {
          -webkit-text-stroke: 2px rgba(52, 152, 219, 0.5); /* 描边效果 */
        }

        .copied-success {
          background-color: #3796FF;
          border-color: rgba(72, 187, 120, 0.5);
        }
        

        .content-auto {
  content-visibility: auto;
}
.card-shadow {
  box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.1);
}
.btn-hover {
  transition: all 300ms ease;
}
.btn-hover:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}
.input-focus:focus {
  outline: none;
  box-shadow: 0 10px 15px -3px rgba(55, 150, 255, 0.1), 0 4px 6px -4px rgba(55, 150, 255, 0.1);
  border-color: rgba(55, 150, 255, 0.5);
}
.version-card {
  cursor: pointer;
    border-radius: 0.375rem;
    border-width: 1px;
    --tw-border-opacity: 1;
    border-color: rgb(201 205 212 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
    padding: 0.75rem;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
}
.version-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 0, 0, 0.5);
  transform: translateY(-1px);
}
.version-card-selected {
  --tw-border-opacity: 1;
    border-color:#3796FF;
    background-color: rgba(55, 150, 255, 0.05);
    --tw-text-opacity: 1;
    color: rgb(29 33 41 / var(--tw-text-opacity, 1));
}
.version-card-selected:hover {
  border-color:#3796FF;
  background-color: rgba(55, 150, 255, 0.05);
}
.input-wrapper {
  position: relative;
}
.clear-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all 200ms ease;
}
.clear-btn:hover {
  color: #4b5563;
}
.input-has-value + .clear-btn {
  opacity: 1;
  visibility: visible;
}
.mobile-input {
  font-size: 0.875rem;
  padding: 0.625rem 0.75rem;
}
.mobile-label {
  font-size: 0.75rem;
}
.close-btn-thin {
  color: #9ca3af;
  font-size: 1.25rem;
  font-weight: 300;
  transition: color 150ms ease;
}
.close-btn-thin:hover {
  color: #333;
}
.form-error {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}
.input-error {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}
.form-success {
  color: #10b981;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}
.btn-loading {
  opacity: 0.7;
  cursor: not-allowed;
}
.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}
.feature-icon {
  margin-top: 0.125rem;
  margin-right: 0.5rem;
  color: #10b981;
}


      }
      input:-webkit-autofill {
          box-shadow: 0 0 0 1000px white inset !important;
          -webkit-text-fill-color: #000; /* 字体颜色 */
        }
      .bac {
        background: url("https://img.medsci.cn/202506/f4a482bb6fad425cbdef4703cfd2a0b2-Da5cD1P9tYzT.png")
          no-repeat;
        background-size: 100% 100%;
      }
      /* 弹窗遮罩层 */

      .modal-overlay {
        position: fixed;

        top: 0;

        left: 0;

        right: 0;

        bottom: 0;

        background-color: rgba(0, 0, 0, 0.5);

        display: none;

        justify-content: center;

        align-items: center;

        z-index: 1000;
      }

      /* 弹窗主体 - 调整为更浅的淡蓝色渐变 */

      .modal-container {
        width: 320px;

        padding: 30px;

        border-radius: 12px;

        border: 2px solid white;

        background: linear-gradient(135deg, #f5fbff 0%, #e6f4ff 100%);

        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);

        text-align: center;

        position: relative;

        backdrop-filter: blur(2px);
      }

      /* 关闭按钮 */

      .modal-close {
        position: absolute;

        top: 8px;

        right: 12px;

        width: 24px;

        height: 24px;

        background: rgba(150, 150, 150, 0.3);

        color: #666;

        border-radius: 50%;

        display: flex;

        justify-content: center;

        align-items: center;

        cursor: pointer;

        font-size: 16px;

        font-weight: bold;

        transition: all 0.3s;

        border: 1px solid rgba(255, 255, 255, 0.5);
      }

      .modal-close:hover {
        background: rgba(100, 100, 100, 0.4);

        color: #333;
      }

      /* 标题样式 */

      .modal-title {
        font-size: 20px;

        font-weight: 600;

        color: #2c3e50;

        margin-bottom: 20px;

        letter-spacing: 0.5px;
      }

      /* 二维码容器 */

      .qrcode-container {
        width: 200px;

        height: 200px;

        margin: 0 auto 20px;

        padding: 12px;

        background: white;

        border-radius: 8px;

        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

        border: 1px solid rgba(0, 0, 0, 0.05);
      }

      .qrcode-container img {
        width: 100%;

        height: 100%;

        object-fit: contain;
      }

      /* 描述文字 */

      .modal-description {
        font-size: 14px;

        color: #4a5568;

        line-height: 1.6;

        margin-top: 15px;

        letter-spacing: 0.3px;
      }

      /* 强调文字 */

      .highlight {
        color: #03bde4;

        font-weight: 500;
      }
    </style>
  </head>
  <body class="bg-dark relative font-inter text-gray-200 min-h-screen">
    <!-- 弹窗HTML结构 -->

    <div class="modal-overlay">
      <div class="modal-container">
        <div class="modal-close" onclick="closeModal()">×</div>

        <div class="modal-title">扫码添加企业微信</div>

        <div class="qrcode-container">
          <!-- 替换为实际的企业微信二维码图片 -->

          <img src="./img/qrcode.jpg" alt="企业微信二维码" />
        </div>

        <div class="modal-description">
          扫描上方二维码，添加我们的企业微信<br />
<!-- 
          或搜索微信号：<span class="highlight">YourCompanyWeChat</span><br /> -->

          工作日<span class="highlight">9:00-18:00</span>在线为您服务
        </div>
      </div>
    </div>

    <!-- 科技线条感的Banner -->
    <header class="relative h-[31.25rem] overflow-hidden flex items-center">
      <!-- 抽象几何图案背景 -->

      <!-- 背景渐变 -->
      <div
        class="absolute inset-0 h-[31.25rem] bg-gradient-to-r from-dark-blue to-dark opacity-90"
      >
        <img
          class="w-full h-full  object-cover"
          src="https://static.medsci.cn/ai-write/ElavaX-0610.webp"
          alt=""
        />
      </div>

      <!-- 内容 -->
      <div class="container mx-auto px-4 relative z-10">
        <div
        class="w-[7rem] h-[7rem] rounded-lg overflow-hidden from-accent to-glow flex items-center justify-center"
      >
        <img
          class="w-full h-full"
          src="https://img.medsci.cn/202506/833d608bb5534a0f886b12075f9fd9ba-UjQbqGOQo7K4.png"
          alt=""
        />
      </div>
        <p
          class="text-[clamp(1.1rem,2vw,1.4rem)] font-medium mt-[1.25rem] !text-[2rem] !line-height-[40px] text-gray-300 max-w-2xl leading-relaxed mb-8"
        >
          <!-- 修正为max-w-2xl -->
          精准剖析，深度洞察，赋能您的卓越创见
        </p>
        
          <!-- 按钮边框改为1.5px -->
          <!-- <a href="https://ai.medsci.cn/chat/elavax-base" target="_blank" title="ElavaX-Base"> -->
            <button
            id="openPopupBtn"
            class="bg-transparent border-[1.5px] border-white rounded-full w-[280px] h-[56px] text-[20px] hover:bg-white/10 transition-all duration-300 transform hover:scale-105 animate-glow"
          >立即体验</button>
        <!-- </a> -->
      </div>
    </header>

    <!-- 订阅区域 -->
    <section id="pricing" class="py-20 sm:py-10 bg-gradient-to-b from-dark to-dark-blue">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16 sm:mb-8">
          <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-white mb-4">
            选择最适合您的ElavaX方案
          </h2>
          <p class="text-gray-400 max-w-2xl mx-auto">
            <!-- 修正为max-w-2xl -->
            根据您的需求选择最适合的评估方案，每一种方案都为您提供专业的评估和优化建议
          </p>
        </div>

        <div class="flex flex-col md:flex-row justify-center gap-8">
          <!-- 基础版 -->
          <div
            class="bg-white/10 pb-[115px] w-full max-w-[378px] rounded-xl p-8 gradient-border-75 card-hover border border-white/10"
          >
            <div class="flex flex-row mb-6">
              <!-- 修改为垂直排列 -->
              <div
                class="w-16 mr-3 h-16 rounded-full bg-accent/20 flex items-center justify-center mb-4"
              >
                <!-- 图标容器放大 -->
                <img
                  src="https://img.medsci.cn/202506/4f0381e8e2ca4ac1a795f63c8253bc3a-CupRzsOnRv2M.png"
                  class="w-full h-full"
                  alt="ElavaX Base"
                />
              </div>
              <div>
                <h3 class="text-xl font-bold text-white text-left">
                  ElavaX Base
                </h3>
                <!-- 标题左对齐 -->
                <p class="text-gray-400 mt-1 text-left">基础评估与优化</p>
                <!-- 副标题左对齐 -->
              </div>
            </div>
            <ul class="space-y-3 mb-8 min-h-[12rem]">
              <li class="flex items-start">
                <i class="fa fa-check text-green-400 mt-1 mr-3"></i>
                <span>对您提交的构想进行单次系统性评估。</span>
              </li>
              <li class="flex items-start">
                <i class="fa fa-check text-green-400 mt-1 mr-3"></i>
                <span>提供针对性的深度修改与优化建议，提升基础质量。</span>
              </li>
            </ul>
            
            <!-- <a href="https://ai.medsci.cn/chat/elavax-base" target="_blank" title="ElavaX-Base"> -->
              <button
              id="openPopupBtnBase"
              class="w-full absolute bottom-[65px] left-8 !w-[calc(100%-64px)] py-3 rounded-xl border border-white/30 text-white font-medium hover:bg-white/10 transition-all duration-300"
            >免费使用</button>
            <!-- </a> -->
            
          </div>

          <!-- Pro版 -->
          <div
            class="bg-white/10 w-full pb-[115px] max-w-[378px] rounded-xl p-8 gradient-border-75 card-hover border border-white/10"
          >
            <div class="flex flex-row mb-6">
              <!-- 修改为垂直排列 -->
              <div
                class="w-16 mr-3 h-16 rounded-full bg-accent/30 flex items-center justify-center mb-4"
              >
                <!-- 图标容器放大 -->
                <img
                  src="https://img.medsci.cn/202506/1a571ddf9d854a648acd25b5f03ab424-YCTVXNnz7Rj7.png"
                  class="w-full h-full"
                  alt="ElavaX Pro"
                />
              </div>
              <div>
                <h3 class="text-xl font-bold text-white text-left">
                  ElavaX Pro
                </h3>
                <!-- 标题左对齐 -->
                <p class="text-gray-400 mt-1 text-left">个性化智能提升</p>
                <!-- 副标题左对齐 -->
              </div>
            </div>
            <ul class="space-y-3 mb-8 min-h-[12rem]">
              <li class="flex items-start">
                <i class="fa fa-check text-green-400 mt-1 mr-3"></i>
                <span>支持个性化深度提问，全面理解评估细节与反馈。</span>
              </li>
              <li class="flex items-start">
                <i class="fa fa-check text-green-400 mt-1 mr-3"></i>
                <span
                  >结合系统评估与您的专属需求，提供精准的定制化提升方案。</span
                >
              </li>
              <li class="flex items-start">
                <i class="fa fa-check text-green-400 mt-1 mr-3"></i>
                <span>智能匹配潜在的目标平台与发展机遇。</span>
              </li>
            </ul>
            <div class="absolute bottom-[65px] left-8 w-[calc(100%-64px)]">
             
            <!-- <a href="https://ai.medsci.cn/chat/elavax-pro" target="_blank" title="ElavaX-Pro"> -->
               <button
              class="w-full  py-3 rounded-lg border border-white/30 text-white font-medium hover:bg-white/10 transition-all duration-300" id="openPopupBtnPro">了解 Pro 版详情</button>
            <!-- </a> -->
           
            <!-- <p class="text-white/70 text-[14px] text-center mt-3">
              每个账号支持20次试用
            </p> -->
            </div>
          </div>

          <!-- Ultra版 -->
          <div
            class="bg-white/10 pb-[115px] w-full max-w-[378px] rounded-xl p-8 gradient-border-75 card-hover border border-white/10 bac"
          >
            <div class="flex flex-row mb-6">
              <div
                class="w-16 mr-3 h-16 rounded-full bg-accent/40 flex items-center justify-center mb-4"
              >
                <img
                  src="https://img.medsci.cn/202506/9ae3be0885dd49a9b0466ff2ce1df07b-crxMbuzWtoIN.png"
                  class="w-full h-full"
                  alt="ElavaX Ultra"
                />
              </div>
              <div>
                <h3 class="text-xl font-bold text-white text-left">
                  ElavaX Ultra
                </h3>
                <p class="text-gray-400 mt-1 text-left">个性化智能提升</p>
              </div>
            </div>
            <ul class="space-y-3 mb-8 min-h-[12rem]">
              <li class="flex items-start">
                <i class="fa fa-check text-green-400 mt-1 mr-3"></i>
                <span
                  >对标顶级标准：
                  融合国际顶级期刊与高水平成果要求进行极致深度评估。</span
                >
              </li>
              <li class="flex items-start">
                <i class="fa fa-check text-green-400 mt-1 mr-3"></i>
                <span
                  >洞察关键 GAP：
                  精准定位您研究/方案中的核心差距与潜在突破口。</span
                >
              </li>
              <li class="flex items-start">
                <i class="fa fa-check text-green-400 mt-1 mr-3"></i>
                <span
                  >启发高阶思路：
                  激发超越常规的创新思维，助力实现高影响力研究突破与成果转化。</span
                >
              </li>
            </ul>
            <button
              id="openPopupBtnUltra"
              class="w-full absolute bottom-[65px] left-8 !w-[calc(100%-64px)] w-full py-3 rounded-lg border border-white/30 text-white font-medium hover:bg-white/10 transition-all duration-300"
            >
              了解 Ultra 专属特权
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark-blue py-8 md:py-12">
      <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <div class="mb-6 md:mb-0">
            <div class="flex flex-row items-center gap-2 md:gap-4 mb-4 flex-nowrap">
              <div
                class="w-[3rem] h-[3rem] md:w-[4rem] md:h-[4rem] overflow-hidden rounded-lg bg-gradient-to-br from-accent to-glow flex items-center justify-center"
              >
                <span class="text-white w-full h-full font-bold">
                  <img
                    class="w-full h-full"
                    src="https://img.medsci.cn/202506/06e46058edd34eea9ae96580a46bf327-FP4DdQMkcnma.png"
                    alt=""
                  />
                </span>
              </div>
              <div class="flex flex-col">
                <div class="flex items-center mb-2 md:mb-1 gap-2  flex-nowrap">
                  <p class="text-white font-medium text-lg md:text-xl">联系我们</p>
                  <i class="fa fa-envelope-o text-accent text-base md:text-lg fa-envelope-o-stroke"></i>
                </div>
                <div class="flex items-center gap-2  flex-nowrap">
                  <span class="text-gray-300 text-sm  line-height-[1] md:text-[0.825rem]"><EMAIL></span>
                  <button
                    id="footerCopyBtn"
                    class="copy-btn bg-white/10 whitespace-nowrap hover:bg-white/20 text-white px-2 md:px-3 py-1 rounded-lg flex items-center gap-1 md:gap-1.5 transition-all duration-300 border border-white/10 h-6 md:h-7 text-xs"
                  >
                    <i class="fa fa-copy"></i>
                    <span>复制</span>
                  </button>
                </div>
              </div>
            </div>
            <p class="text-gray-400 max-w-sm md:max-w-md text-sm md:text-base">
              精准剖析，深度洞察，赋能您的卓越创见
            </p>
          </div>
        </div>
        <div
          class="border-t border-gray-800 mt-6 md:mt-10 pt-4 md:pt-6 flex flex-col md:flex-row justify-between items-center"
        >
          <p class="text-gray-500 text-xs md:text-sm mb-4 md:mb-0">
            © 2025 ElavaX. 保留所有权利。
          </p>
        </div>
      </div>
    </footer>
    <div id="overlay" class="fixed inset-0 bg-black/60 backdrop-blur-sm z-40 hidden"></div>

  <div id="trialPopup" class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-md bg-white rounded-lg shadow-2xl z-50 hidden overflow-hidden border border-light-border/50">
    <div class="p-3 md:p-5">
      <div class="flex justify-between items-start mb-3 md:mb-5">
        <div>
          <h3 class="text-xl font-bold text-[#3796FF]">请选择试用版本</h3>
          <p class="text-xs text-gray-500 mt-0.5">选择最适合您的版本开始试用</p>
        </div>
        <button id="closePopupBtn" class="close-btn-thin">
          <i class="fa fa-times"></i>
        </button>
      </div>

      <div class="mb-4 md:mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-2 md:gap-3">
          <button class="version-btn version-card version-card-selected" data-version="base">
            <div class="font-semibold text-base text-center text-[#000]">ElavaX Base</div>
            <div class="text-xs text-gray-500 text-center">基础功能体验</div>
          </button>
          <button class="version-btn version-card" data-version="pro">
            <div class="font-semibold text-base text-center text-[#000]">ElavaX Pro</div>
            <div class="text-xs text-gray-500 text-center">专业功能体验</div>
          </button>
          <button class="version-btn version-card" data-version="ultra">
            <div class="font-semibold text-base text-center text-[#000]">ElavaX Ultra</div>
            <div class="text-xs text-gray-500 text-center">高级功能体验</div>
          </button>
        </div>
      </div>

      <form id="trialForm" class="space-y-3">
        <div class="form-group">
          <label for="email" class="block text-sm font-medium text-gray-700 mb-0.5"><span class="input-error">*</span>邮箱</label>
          <div class="input-wrapper">
            <input type="email" id="email" name="email" maxlength="100" class="w-full text-[#000] bg-white border border-light-border rounded-md mobile-input pl-3 pr-8 input-focus transition-all" placeholder="请输入您的邮箱">
            <span class="clear-btn" data-input="email">
              <i class="fa fa-times-circle"></i>
            </span>
          </div>
          <p id="emailError" class="form-error hidden">请输入有效的邮箱地址</p>
        </div>

        <div class="form-group">
          <label for="company" class="block text-sm font-medium text-gray-700 mb-0.5"><span class="input-error">*</span>单位</label>
          <div class="input-wrapper">
            <input type="text" id="company" name="company" maxlength="255" class="w-full text-[#000] bg-white border border-light-border rounded-md mobile-input pl-3 pr-8 input-focus transition-all" placeholder="请输入您的单位名称">
            <span class="clear-btn" data-input="company">
              <i class="fa fa-times-circle"></i>
            </span>
          </div>
          <p id="companyError" class="form-error hidden">请输入您的单位名称</p>
        </div>

        <div class="form-group">
          <label for="professional" class="block text-sm font-medium text-gray-700 mb-0.5"><span class="input-error">*</span>职称</label>
          <div class="input-wrapper">
            <input type="text" id="professional" name="professional" maxlength="255" class="w-full text-[#000] bg-white border border-light-border rounded-md mobile-input pl-3 pr-8 input-focus transition-all" placeholder="请输入您的职称">
            <span class="clear-btn" data-input="professional">
              <i class="fa fa-times-circle"></i>
            </span>
          </div>
          <p id="professionalError" class="form-error hidden">请输入您的职称</p>
        </div>

        <div class="form-group">
          <label for="mobile" class="block text-sm font-medium text-gray-700 mb-0.5"><span class="input-error">*</span>手机号</label>
          <div class="input-wrapper">
            <input type="tel" id="mobile" name="mobile" class="w-full text-[#000] bg-white border border-light-border rounded-md mobile-input pl-3 pr-8 input-focus transition-all" placeholder="请输入您的手机号">
            <span class="clear-btn" data-input="phone">
              <i class="fa fa-times-circle"></i>
            </span>
          </div>
          <p id="mobileError" class="form-error hidden">请输入有效的手机号</p>
        </div>

        <div class="flex justify-between space-x-2 md:space-x-3 mt-4 md:mt-5">
          <div class="text-xs text-gray-500 flex items-center">申请版本并非最终试用版本</div>
          <div><button type="button" id="cancelBtn" class="px-3 md:px-4 py-1.5 md:py-2 bg-white border border-light-border text-gray-700 rounded-md hover:bg-gray-50 transition-colors btn-hover text-xs md:text-sm">
            取消
          </button>
          <button type="submit" id="submitBtn" class="px-3 md:px-4 py-1.5 md:py-2 bg-[#3796FF] text-white rounded-md  transition-colors btn-hover text-xs md:text-sm">
            提交申请
          </button></div>
        </div>
      </form>
    </div>
  </div>

  <!-- 成功提交消息 -->
  <div id="successMessage" class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-md bg-white rounded-lg shadow-2xl z-50 hidden overflow-hidden border border-light-border/50">
    <div class="p-3 md:p-5 text-center">
      <div class="text-success text-5xl mb-3">
        <i class="fa fa-check-circle"></i>
      </div>
      <h3 class="text-xl font-bold text-dark mb-2">申请提交成功！</h3>
      <p class="text-gray-600 mb-4">试用申请已提交。我们会尽快联系您，请耐心等待...</p>
      <button id="closeSuccessBtn" class="px-4 py-2 bg-primary text-white rounded-md  transition-colors btn-hover">
        确定
      </button>
    </div>
  </div>
    <script>
      // // 导航栏滚动效果
      // const navbar = document.getElementById('navbar');
      // window.addEventListener('scroll', () => {
      //     if (window.scrollY > 50) {
      //         navbar.classList.add('bg-dark/80', 'backdrop-blur-md', 'shadow-lg');
      //     } else {
      //         navbar.classList.remove('bg-dark/80', 'backdrop-blur-md', 'shadow-lg');
      //     }
      // });
    </script>
  </body>
  <script>
    // 关闭弹窗函数
    function closeModal() {
      document.querySelector(".modal-overlay").style.display = "none";
    }
    function openModal() {
      document.querySelector(".modal-overlay").style.display = "flex";
    }

    // 点击遮罩层也可以关闭弹窗

    document
      .querySelector(".modal-overlay")
      .addEventListener("click", function (e) {
        if (e.target === this) {
          closeModal();
        }
      });

    // 按ESC键关闭弹窗

    document.addEventListener("keydown", function (e) {
      if (e.key === "Escape") {
        closeModal();
      }
    });
    // 复制邮箱功能

    const footerCopyBtn = document.getElementById("footerCopyBtn");

    function copyEmail(button) {
      // 创建临时文本区域

      const tempTextArea = document.createElement("textarea");

      tempTextArea.value = "<EMAIL>";

      document.body.appendChild(tempTextArea);

      // 选中并复制文本

      tempTextArea.select();

      document.execCommand("copy");

      // 移除临时文本区域

      document.body.removeChild(tempTextArea);

      // 更新按钮状态

      if (button) {
        const originalText = button.querySelector("span").textContent;

        button.querySelector("span").textContent = "已复制";

        button.classList.add("copied-success");

        // 2秒后恢复按钮状态

        setTimeout(() => {
          button.querySelector("span").textContent = originalText;

          button.classList.remove("copied-success");
        }, 2000);
      }

      // 创建并显示复制成功提示

      const notification = document.createElement("div");

      notification.className =
        "fixed top-[2%] left-1/2 transform -translate-y-1/10 -translate-x-1/2 bg-[#3796FF] px-2 py-1 text-[14px] rounded-[4px] shadow-lg z-50 opacity-0 transition-opacity duration-300";

      notification.textContent = "复制成功！";

      document.body.appendChild(notification);

      // 显示提示

      setTimeout(() => {
        notification.classList.remove("opacity-0");
      }, 10);

      // 3秒后隐藏提示

      setTimeout(() => {
        notification.classList.add("opacity-0");

        setTimeout(() => {
          document.body.removeChild(notification);
        }, 300);
      }, 3000);
    }

    footerCopyBtn.addEventListener("click", () => copyEmail(footerCopyBtn));
    document.addEventListener('DOMContentLoaded', function() {
      const overlay = document.getElementById('overlay');
      const trialPopup = document.getElementById('trialPopup');
      const successMessage = document.getElementById('successMessage');
      const openPopupBtn = document.getElementById('openPopupBtn');
      const openPopupBtnPro = document.getElementById('openPopupBtnPro');
      const openPopupBtnUltra = document.getElementById('openPopupBtnUltra');
      const openPopupBtnBase = document.getElementById('openPopupBtnBase');
      const closePopupBtn = document.getElementById('closePopupBtn');
      const cancelBtn = document.getElementById('cancelBtn');
      const closeSuccessBtn = document.getElementById('closeSuccessBtn');
      const versionBtns = document.querySelectorAll('.version-btn');
      const trialForm = document.getElementById('trialForm');
      const inputs = document.querySelectorAll('input');
      const clearButtons = document.querySelectorAll('.clear-btn');
      const submitBtn = document.getElementById('submitBtn');
      const versionDetails = document.querySelectorAll('.version-details-content');
      
      // 表单验证函数
      function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
      }
      
      function validatePhone(phone) {
        const re = /^1[3-9]\d{9}$/;
        return re.test(phone);
      }
      
      function validateForm() {
        let isValid = true;
        const email = document.getElementById('email').value.trim();
        const company = document.getElementById('company').value.trim();
        const mobile = document.getElementById('mobile').value.trim();
        const professional = document.getElementById('professional').value.trim();
        
        // 验证邮箱
        if (!email) {
          document.getElementById('emailError').textContent = '请输入邮箱地址';
          document.getElementById('emailError').classList.remove('hidden');
          isValid = false;
        } else if (!validateEmail(email)) {
          document.getElementById('emailError').textContent = '请输入有效的邮箱地址';
          document.getElementById('emailError').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('emailError').classList.add('hidden');
        }
        
        // 验证单位
        if (!company) {
          document.getElementById('companyError').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('companyError').classList.add('hidden');
        }
         // 验证职称
         if (!professional) {
          document.getElementById('professionalError').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('professionalError').classList.add('hidden');
        }
        
        // 验证手机号
        if (!mobile) {
          document.getElementById('mobileError').textContent = '请输入手机号';
          document.getElementById('mobileError').classList.remove('hidden');
          isValid = false;
        } else if (!validatePhone(mobile)) {
          document.getElementById('mobileError').textContent = '请输入有效的手机号';
          document.getElementById('mobileError').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('mobileError').classList.add('hidden');
        }
        
        return isValid;
      }
      
      // 显示版本详情
      function showVersionDetails(version) {
        // versionDetails.forEach(detail => {
        //   detail.classList.add('hidden');
        // });
        // document.getElementById(`${version}Details`).classList.remove('hidden');
      }

      function openPopup(type) {
        if(type === 'pro'){
          versionBtns.forEach((item)=>{
            item.classList.remove('version-card-selected');
          })
          versionBtns[1].classList.add('version-card-selected');
        }
        if(type === 'ultra'){
          versionBtns.forEach((item)=>{
            item.classList.remove('version-card-selected');
          })
          versionBtns[2].classList.add('version-card-selected');
        }
        if(type === 'base'){
          versionBtns.forEach((item)=>{
            item.classList.remove('version-card-selected');
          })
          versionBtns[0].classList.add('version-card-selected');
        }
        overlay.classList.remove('hidden');
        trialPopup.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        trialPopup.animate([
          { opacity: 0, transform: 'translate(-50%, -50%) scale(0.95)' },
          { opacity: 1, transform: 'translate(-50%, -50%) scale(1)' }
        ], {
          duration: 300,
          easing: 'cubic-bezier(0.16, 1, 0.3, 1)'
        });
      }

      function closePopup() {
        const animation = trialPopup.animate([
          { opacity: 1, transform: 'translate(-50%, -50%) scale(1)' },
          { opacity: 0, transform: 'translate(-50%, -50%) scale(0.95)' }
        ], {
          duration: 200,
          easing: 'cubic-bezier(0.4, 0, 1, 1)'
        });
        
        animation.onfinish = () => {
          overlay.classList.add('hidden');
          trialPopup.classList.add('hidden');
          document.body.style.overflow = '';
          // 重置表单
          trialForm.reset();
          inputs.forEach(input => {
            input.classList.remove('input-has-value');
          });
          // 隐藏错误消息
          document.querySelectorAll('.form-error').forEach(error => {
            error.classList.add('hidden');
          });
        };
      }
      
       function showSuccessMessage() {
        trialPopup.classList.add('hidden');
        successMessage.classList.remove('hidden');
        successMessage.animate([
          { opacity: 0, transform: 'translate(-50%, -50%) scale(0.95)' },
          { opacity: 1, transform: 'translate(-50%, -50%) scale(1)' }
        ], {
          duration: 300,
          easing: 'cubic-bezier(0.16, 1, 0.3, 1)'
        });
      }
      
      function closeSuccessMessage() {
        const animation = successMessage.animate([
          { opacity: 1, transform: 'translate(-50%, -50%) scale(1)' },
          { opacity: 0, transform: 'translate(-50%, -50%) scale(0.95)' }
        ], {
          duration: 200,
          easing: 'cubic-bezier(0.4, 0, 1, 1)'
        });
        
        animation.onfinish = () => {
          overlay.classList.add('hidden');
          successMessage.classList.add('hidden');
          document.body.style.overflow = '';
          // 重置表单
          trialForm.reset();
          inputs.forEach(input => {
            input.classList.remove('input-has-value');
          });
        };
      }

      openPopupBtnBase.addEventListener('click', function() {openPopup('base');});
      openPopupBtn.addEventListener('click', function() {openPopup('base');});
      closePopupBtn.addEventListener('click', closePopup);
      openPopupBtnPro.addEventListener('click', function() {openPopup('pro');});
      openPopupBtnUltra.addEventListener('click', function() {openPopup('ultra');});
      cancelBtn.addEventListener('click', closePopup);
      overlay.addEventListener('click', () => {
        if (!trialPopup.classList.contains('hidden')) {
          closePopup();
        } else if (!successMessage.classList.contains('hidden')) {
          closeSuccessMessage();
        }
      });
      closeSuccessBtn.addEventListener('click', closeSuccessMessage);

      versionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          versionBtns.forEach(b => {
            b.classList.remove('version-card-selected');
          });
          this.classList.add('version-card-selected');
          // 显示对应版本详情
          showVersionDetails(this.getAttribute('data-version'));
        });
      });

      trialForm.addEventListener('submit',async function(e) {
        e.preventDefault();
        let res = '';
        versionBtns.forEach((item,index)=>{
              console.log(item.classList.contains('version-card-selected'));
              if(item.classList.contains('version-card-selected')){
                res = index == 0 ? 'elavax-base' : index == 1 ? 'elavax-pro' : 'elavax-ultra';
              }
            })
            console.log(res);
        if (validateForm()) {
          // 显示加载状态
          submitBtn.classList.add('btn-loading');
          submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i> 提交中...';
          submitBtn.classList.remove('btn-loading');
            submitBtn.innerHTML = '提交申请';
            await fetch(location.origin + '/dev-api/ai-base/index/saveCollect', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email: document.getElementById('email').value,
            company: document.getElementById('company').value,
            mobile: document.getElementById('mobile').value,
            professional: document.getElementById('professional').value,
            appNameEn:res
          })
        })
        .then(response => {
          if (!response.ok) {
            throw new Error(`请求失败: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
           // 模拟提交到服务器
           setTimeout( () => {
            // 恢复按钮状态
           
            // 显示成功消息
            showSuccessMessage();
          }, 1500);
        })
         
        }
      });

      // 监听输入事件，控制清除按钮显示/隐藏
      inputs.forEach(input => {
        input.addEventListener('input', function() {
          if (this.value.trim() !== '') {
            this.classList.add('input-has-value');
          } else {
            this.classList.remove('input-has-value');
          }
          
          // 实时验证邮箱和手机号
          if (this.id === 'email') {
            if (this.value.trim() && !validateEmail(this.value.trim())) {
              document.getElementById('emailError').textContent = '邮箱格式不正确';
              document.getElementById('emailError').classList.remove('hidden');
            } else {
              document.getElementById('emailError').classList.add('hidden');
            }
          } else if (this.id === 'mobile') {
            if (this.value.trim() && !validatePhone(this.value.trim())) {
              document.getElementById('mobileError').textContent = '手机号格式不正确';
              document.getElementById('mobileError').classList.remove('hidden');
            } else {
              document.getElementById('mobileError').classList.add('hidden');
            }
          }
        });

        // 初始化检查
        if (input.value.trim() !== '') {
          input.classList.add('input-has-value');
        }
      });

      // 清除按钮点击事件
      clearButtons.forEach(button => {
        button.addEventListener('click', function() {
          const inputId = this.getAttribute('data-input');
          const input = document.getElementById(inputId);
          if (input) {
            input.value = '';
            input.classList.remove('input-has-value');
            input.focus();
            
            // 隐藏对应的错误消息
            if (inputId === 'email') {
              document.getElementById('emailError').classList.add('hidden');
            } else if (inputId === 'company') {
              document.getElementById('companyError').classList.add('hidden');
            } else if (inputId === 'mobile') {
              document.getElementById('mobileError').classList.add('hidden');
            }else if (inputId === 'professional') {
              document.getElementById('professionalError').classList.add('hidden');
            }
          }
        });
      });

      // 优化移动端键盘弹出时的布局
      window.addEventListener('resize', function() {
        if (document.activeElement.tagName === 'INPUT' && window.innerHeight < 600) {
          trialPopup.style.transform = 'translate(-50%, -40%) scale(1)';
          successMessage.style.transform = 'translate(-50%, -40%) scale(1)';
        } else {
          trialPopup.style.transform = 'translate(-50%, -50%) scale(1)';
          successMessage.style.transform = 'translate(-50%, -50%) scale(1)';
        }
      });

      // 检查URL参数，apply=upgrade时自动弹窗
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('apply') === 'upgrade') {
        openPopup('pro');
      }
    });
  </script>
</html>