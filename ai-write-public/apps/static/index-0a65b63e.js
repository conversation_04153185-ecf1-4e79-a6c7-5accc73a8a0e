import{N as e,u as t,a,r as i,a3 as s,o as n,y as r,v as o,d as l,e as d,j as u,t as c,F as p,z as m,k as v,$ as f,ag as x,X as g,ah as b,ai as y,ae as w,af as h}from"https://static.medsci.cn/ai-write/static/index-62ea6ab9.js";import{_ as k}from"./_plugin-vue_export-helper-1b428a4d.js";const _=e=>(w("data-v-9bf00e90"),e=e(),h(),e),O={class:"px-3 py-16"},j={class:"mx-auto mb-12 max-w-screen-md text-center"},U={class:"mt-1 text-3xl font-bold"},I={class:"mt-2 text-lg text-muted-foreground"},$={class:"mx-auto max-w-screen-lg"},C={class:"grid grid-cols-1 gap-x-6 gap-y-8 md:grid-cols-3"},L={class:"text-lg font-semibold"},N={class:"mt-3 flex items-center justify-center"},z={class:"text-5xl font-bold"},q={class:"ml-1 text-muted-foreground"},B=["onClick","disabled"],D={class:"mt-8 space-y-3"},M=_((()=>u("svg",{class:"mr-1 size-6 stroke-current stroke-2 text-purple-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},[u("path",{d:"M0 0h24v24H0z",stroke:"none"}),u("path",{d:"M5 12l5 5L20 7"})],-1))),R={class:"px-3 py-16"},T={class:"mx-auto max-w-screen-lg"},F={class:"w-full","data-orientation":"vertical"},H={"data-orientation":"vertical",class:"flex"},J=["aria-controls","aria-expanded","onClick"],P=[_((()=>u("path",{d:"m9 18 6-6-6-6"},null,-1)))],S=["id","hidden","aria-labelledby"],X=k(e({__name:"index",setup(e){const w=t(),h=a(),k=i([{id:"R2cufncva",title:"Lorem ipsum dolor sit amet, consectetur adipiscing elit?",isOpen:!1},{id:"R2kufncva",title:"Lorem ipsum dolor sit amet, consectetur adipiscing elit?",isOpen:!1}]),_=i({appUuid:"",appLang:s()}),X=i([]),A=i({}),E=i({});n((async()=>{E.value=r.get("userInfo")?JSON.parse(r.get("userInfo")):null,_.value.appUuid=h.query.appUuid;const e=await o(_.value);X.value=e[0].feeTypes,A.value=e[0]}));const G=async()=>{let e=g("current_location_country",1);if(!e){const t=await b();y("current_location_country",t),e=t}return e};return(e,t)=>(l(),d("div",null,[u("div",O,[u("div",j,[u("div",U,c(A.value.appName),1),u("div",I,c(A.value.appDescription),1)]),u("div",$,[u("div",C,[(l(!0),d(p,null,m(X.value,(t=>{var a,i;return l(),d("div",{class:"rounded-xl border border-border px-6 py-8 text-center",key:t.type},[u("div",L,c(e.$t(`tool.${t.type}`)),1),u("div",N,[u("div",z,c("人民币"==t.coinType?"￥":"$")+c(t.feePrice),1),u("div",q,c(),1)]),u("button",{class:"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 rounded-md px-3 mt-5 w-full",onClick:e=>(async e=>{var t;let a=await G();if(null==(t=E.value)?void 0:t.userId){const t={appUuid:h.query.appUuid,priceId:e.priceId,monthNum:e.monthNum};let a=await x(t);location.href=a}else a&&"中国"!=a?w.push("/login"):window.addLoginDom()})(t),disabled:1==(null==(a=A.value.appUser)?void 0:a.status)},c(1==(null==(i=A.value.appUser)?void 0:i.status)?e.$t("market.subscribed"):e.$t("market.subscribe")),9,B),u("ul",D,[(l(!0),d(p,null,m(t.features,(e=>(l(),d("li",{class:"flex items-center text-muted-foreground",key:e},[M,v(" "+c(e),1)])))),128))])])})),128))])])]),u("div",R,[u("div",T,[u("div",F,[(l(!0),d(p,null,m(k.value,((e,t)=>(l(),d("div",{key:t,class:"border-b"},[u("h3",H,[u("button",{type:"button","aria-controls":"radix-"+e.id,"aria-expanded":e.isOpen,onClick:t=>(e=>{e.isOpen=!e.isOpen})(e),class:"flex flex-1 items-center justify-between py-5 text-left text-lg font-medium transition-all hover:no-underline"},[v(c(e.title)+" ",1),(l(),d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:f(["lucide lucide-chevron-right size-4 shrink-0 transition-transform duration-200",{"rotate-90":e.isOpen}])},P,2))],8,J)]),u("div",{id:"radix-"+e.id,hidden:!e.isOpen,role:"region","aria-labelledby":"radix-"+e.id,class:f(["overflow-hidden text-base text-muted-foreground transition-all",{"animate-accordion-up":!e.isOpen,"animate-accordion-down":e.isOpen}])},null,10,S)])))),128))])])])]))}}),[["__scopeId","data-v-9bf00e90"]]);export{X as default};
