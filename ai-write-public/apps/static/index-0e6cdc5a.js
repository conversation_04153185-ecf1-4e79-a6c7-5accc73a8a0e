import{M as e,b as a,u as n,o as i,a7 as s,aE as t,d as o,e as l}from"./index-c0a62e28.js";const c=e({__name:"index",setup(e){const{t:c}=a(),r=n(),p=JSON.parse(decodeURIComponent(r.params.payInfo));return i((async()=>{const e=navigator.userAgent;let a=(new Date).getTime();if((null==p?void 0:p.time)&&a>p.time&&location.replace(location.origin),null!=e)if(e.includes("MicroMessenger"))s.warning(c("tool.pleasescanwithalipay"));else if(e.includes("AlipayClient")){p.time=a;const e=await t(p);location.replace(e)}})),(e,a)=>(o(),l("div"))}});export{c as default};
