/* empty css                  */import{g as e,i as a}from"./index-b62c2a44.js";import{r as l,x as t,u as o,w as i,d as n,e as s,j as r,t as u,g as d,h as c,m as v,F as p,y as m,f,z as g,k as h,i as y,A as b,B as w,C as x,D as _,G as k,E as I,H as S,I as C,J as T,K as $,L as z,M as N,N as q,O as B,P as j,o as U,Q as A,R as V,S as O,T as R,q as E,U as M,V as L,W as H,X as D,a as P,b as W,Y as J,Z as F,$ as X,a0 as Z,a1 as Y,a2 as G,a3 as K,c as Q,a4 as ee,p as ae,a5 as le,a6 as te,a7 as oe,a8 as ie,a9 as ne,aa as se,ab as re,ac as ue,v as de}from"./index-516ef714.js";/* empty css                  *//* empty css                  *//* empty css                 */import{c as ce,r as ve,g as pe,s as me,i as fe,o as ge,a as he,n as ye,m as be,b as we,u as xe,d as _e,e as ke,f as Ie,h as Se,j as Ce,k as Te,w as $e,l as ze,p as Ne,t as qe,q as Be,v as je,x as Ue,y as Ae,z as Ve,A as Oe,B as Re,C as Ee,D as Me,E as Le,F as He,G as De,H as Pe,I as We,J as Je,K as Fe,L as Xe,M as Ze,N as Ye,O as Ge,P as Ke,Q as Qe}from"./index-9fb2500a.js";import{r as ea,a as aa,t as la,f as ta}from"./lang-425e00c5.js";/* empty css                   */const oa={class:"p-3 flex-1 rounded-md"},ia={class:"text-[14px] font-bold mb-2 text-gray-600"},na={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(e,{expose:a,emit:C}){const T=l(t.get("userInfo")?JSON.parse(t.get("userInfo")):{}),$=o(),z=l([]),N=l({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),q=l(""),B=e,j=B.type,U=B.fileVerify,A=B.label,V=B.required,O=B.max_length,R=B.options;"file"==j&&(q.value=null),"file-list"==j&&(q.value=[]);const E={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},M=()=>{let e="";return U.forEach(((a,l)=>{l<U.length-1?e+=E[a].join(",")+",":e+=E[a].join(",")})),e},L=C,H=(e,a,l)=>{},D=()=>{q.value=""},P=async e=>{const{file:a,onSuccess:l,onError:t}=e,o=new FormData;o.append("file",a),o.append("appId",$.params.uuid),o.append("user",T.value.userName);try{const e=await b(o);"file-list"==j?q.value.push({type:N.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id}):q.value={type:N.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},l(e,a)}catch(i){t(i)}return!1};R&&R.length>0&&(q.value=R[0]);return a({updateMessage:()=>{R&&R.length>0?q.value=R[0]:"file"==j?(q.value=null,z.value=[]):"file-list"==j?(q.value=[],z.value=[]):q.value=""}}),i(q,(e=>{L("update:value",e)}),{immediate:!0,deep:!0}),(e,a)=>{const l=w,t=x,o=_,i=k,b=I,C=S;return n(),s("div",oa,[r("div",ia,u(d(A)),1),"paragraph"===d(j)||"text-input"===d(j)?(n(),c(l,{key:0,modelValue:q.value,"onUpdate:modelValue":a[0]||(a[0]=e=>q.value=e),type:"paragraph"===d(j)?"textarea":"text",rows:5,required:d(V),placeholder:`${d(A)}`,"show-word-limit":"",resize:"none",maxlength:d(O)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===d(j)?(n(),c(l,{key:1,modelValue:q.value,"onUpdate:modelValue":a[1]||(a[1]=e=>q.value=e),modelModifiers:{number:!0},type:"number",required:d(V),placeholder:`${d(A)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===d(j)?(n(),c(o,{key:2,modelValue:q.value,"onUpdate:modelValue":a[2]||(a[2]=e=>q.value=e),required:d(V),placeholder:`${d(A)}`},{default:v((()=>[(n(!0),s(p,null,m(d(R),(e=>(n(),c(t,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===d(j)||"file-list"===d(j)?(n(),c(C,{key:3,"file-list":z.value,"onUpdate:fileList":a[3]||(a[3]=e=>z.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":D,"before-remove":e.beforeRemove,limit:d(O),accept:M(),"auto-upload":!0,"on-Success":H,"http-request":P,"on-exceed":e.handleExceed},{default:v((()=>[f(b,{disabled:"file"===d(j)?1==z.value.length:z.value.length==d(O)},{default:v((()=>[f(i,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:v((()=>[f(d(g))])),_:1}),a[4]||(a[4]=h("从本地上传"))])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","limit","accept","on-exceed"])):y("",!0)])}}};let sa=0;function ra(){const e=C(),{name:a="unknown"}=(null==e?void 0:e.type)||{};return`${a}-${++sa}`}function ua(e,a){if(!fe||!window.IntersectionObserver)return;const l=new IntersectionObserver((e=>{a(e[0].intersectionRatio>0)}),{root:document.body}),t=()=>{e.value&&l.unobserve(e.value)};$(t),z(t),ge((()=>{e.value&&l.observe(e.value)}))}const[da,ca]=he("sticky");const va=Ne(N({name:da,props:{zIndex:ye,position:be("top"),container:Object,offsetTop:we(0),offsetBottom:we(0)},emits:["scroll","change"],setup(e,{emit:a,slots:t}){const o=l(),n=xe(o),s=q({fixed:!1,width:0,height:0,transform:0}),r=l(!1),u=B((()=>_e("top"===e.position?e.offsetTop:e.offsetBottom))),d=B((()=>{if(r.value)return;const{fixed:e,height:a,width:l}=s;return e?{width:`${l}px`,height:`${a}px`}:void 0})),c=B((()=>{if(!s.fixed||r.value)return;const a=ke(Ie(e.zIndex),{width:`${s.width}px`,height:`${s.height}px`,[e.position]:`${u.value}px`});return s.transform&&(a.transform=`translate3d(0, ${s.transform}px, 0)`),a})),v=()=>{if(!o.value||Ce(o))return;const{container:l,position:t}=e,i=Te(o),n=pe(window);if(s.width=i.width,s.height=i.height,"top"===t)if(l){const e=Te(l),a=e.bottom-u.value-s.height;s.fixed=u.value>i.top&&e.bottom>0,s.transform=a<0?a:0}else s.fixed=u.value>i.top;else{const{clientHeight:e}=document.documentElement;if(l){const a=Te(l),t=e-a.top-u.value-s.height;s.fixed=e-u.value<i.bottom&&e>a.top,s.transform=t<0?-t:0}else s.fixed=e-u.value<i.bottom}(e=>{a("scroll",{scrollTop:e,isFixed:s.fixed})})(n)};return i((()=>s.fixed),(e=>a("change",e))),Se("scroll",v,{target:n,passive:!0}),ua(o,v),i([$e,ze],(()=>{o.value&&!Ce(o)&&s.fixed&&(r.value=!0,j((()=>{const e=Te(o);s.width=e.width,s.height=e.height,r.value=!1})))})),()=>{var e;return f("div",{ref:o,style:d.value},[f("div",{class:ca({fixed:s.fixed&&!r.value}),style:c.value},[null==(e=t.default)?void 0:e.call(t)])])}}})),[pa,ma]=he("swipe"),fa={loop:qe,width:ye,height:ye,vertical:Boolean,autoplay:we(0),duration:we(500),touchable:qe,lazyRender:Boolean,initialSwipe:we(0),indicatorColor:String,showIndicators:qe,stopPropagation:qe},ga=Symbol(pa);const ha=Ne(N({name:pa,props:fa,emits:["change","dragStart","dragEnd"],setup(e,{emit:a,slots:t}){const o=l(),n=l(),s=q({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let r=!1;const u=Be(),{children:d,linkChildren:c}=je(ga),v=B((()=>d.length)),p=B((()=>s[e.vertical?"height":"width"])),m=B((()=>e.vertical?u.deltaY.value:u.deltaX.value)),g=B((()=>{if(s.rect){return(e.vertical?s.rect.height:s.rect.width)-p.value*v.value}return 0})),h=B((()=>p.value?Math.ceil(Math.abs(g.value)/p.value):v.value)),y=B((()=>v.value*p.value)),b=B((()=>(s.active+v.value)%v.value)),w=B((()=>{const a=e.vertical?"vertical":"horizontal";return u.direction.value===a})),x=B((()=>{const a={transitionDuration:`${s.swiping?0:e.duration}ms`,transform:`translate${e.vertical?"Y":"X"}(${+s.offset.toFixed(2)}px)`};if(p.value){const l=e.vertical?"height":"width",t=e.vertical?"width":"height";a[l]=`${y.value}px`,a[t]=e[t]?`${e[t]}px`:""}return a})),_=(a,l=0)=>{let t=a*p.value;e.loop||(t=Math.min(t,-g.value));let o=l-t;return e.loop||(o=Ee(o,g.value,0)),o},k=({pace:l=0,offset:t=0,emitChange:o})=>{if(v.value<=1)return;const{active:i}=s,n=(a=>{const{active:l}=s;return a?e.loop?Ee(l+a,-1,v.value):Ee(l+a,0,h.value):l})(l),r=_(n,t);if(e.loop){if(d[0]&&r!==g.value){const e=r<g.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==r){const e=r>0;d[v.value-1].setOffset(e?-y.value:0)}}s.active=n,s.offset=r,o&&n!==i&&a("change",b.value)},I=()=>{s.swiping=!0,s.active<=-1?k({pace:v.value}):s.active>=v.value&&k({pace:-v.value})},S=()=>{I(),u.reset(),Oe((()=>{s.swiping=!1,k({pace:1,emitChange:!0})}))};let C;const T=()=>clearTimeout(C),N=()=>{T(),+e.autoplay>0&&v.value>1&&(C=setTimeout((()=>{S(),N()}),+e.autoplay))},V=(a=+e.initialSwipe)=>{if(!o.value)return;const l=()=>{var l,t;if(!Ce(o)){const a={width:o.value.offsetWidth,height:o.value.offsetHeight};s.rect=a,s.width=+(null!=(l=e.width)?l:a.width),s.height=+(null!=(t=e.height)?t:a.height)}v.value&&-1===(a=Math.min(v.value-1,a))&&(a=v.value-1),s.active=a,s.swiping=!0,s.offset=_(a),d.forEach((e=>{e.setOffset(0)})),N()};Ce(o)?j().then(l):l()},O=()=>V(s.active);let R;const E=a=>{!e.touchable||a.touches.length>1||(u.start(a),r=!1,R=Date.now(),T(),I())},M=()=>{if(!e.touchable||!s.swiping)return;const l=Date.now()-R,t=m.value/l;if((Math.abs(t)>.25||Math.abs(m.value)>p.value/2)&&w.value){const a=e.vertical?u.offsetY.value:u.offsetX.value;let l=0;l=e.loop?a>0?m.value>0?-1:1:0:-Math[m.value>0?"ceil":"floor"](m.value/p.value),k({pace:l,emitChange:!0})}else m.value&&k({pace:0});r=!1,s.swiping=!1,a("dragEnd",{index:b.value}),N()},L=(a,l)=>{const t=l===b.value,o=t?{backgroundColor:e.indicatorColor}:void 0;return f("i",{style:o,class:ma("indicator",{active:t})},null)};return Ue({prev:()=>{I(),u.reset(),Oe((()=>{s.swiping=!1,k({pace:-1,emitChange:!0})}))},next:S,state:s,resize:O,swipeTo:(a,l={})=>{I(),u.reset(),Oe((()=>{let t;t=e.loop&&a===v.value?0===s.active?0:a:a%v.value,l.immediate?Oe((()=>{s.swiping=!1})):s.swiping=!1,k({pace:t-s.active,emitChange:!0})}))}}),c({size:p,props:e,count:v,activeIndicator:b}),i((()=>e.initialSwipe),(e=>V(+e))),i(v,(()=>V(s.active))),i((()=>e.autoplay),N),i([$e,ze,()=>e.width,()=>e.height],O),i(Ae(),(e=>{"visible"===e?N():T()})),U(V),A((()=>V(s.active))),Ve((()=>V(s.active))),$(T),z(T),Se("touchmove",(l=>{if(e.touchable&&s.swiping&&(u.move(l),w.value)){!e.loop&&(0===s.active&&m.value>0||s.active===v.value-1&&m.value<0)||(Re(l,e.stopPropagation),k({offset:m.value}),r||(a("dragStart",{index:b.value}),r=!0))}}),{target:n}),()=>{var a;return f("div",{ref:o,class:ma()},[f("div",{ref:n,style:x.value,class:ma("track",{vertical:e.vertical}),onTouchstartPassive:E,onTouchend:M,onTouchcancel:M},[null==(a=t.default)?void 0:a.call(t)]),t.indicator?t.indicator({active:b.value,total:v.value}):e.showIndicators&&v.value>1?f("div",{class:ma("indicators",{vertical:e.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[ya,ba]=he("tabs");var wa=N({name:ya,props:{count:Me(Number),inited:Boolean,animated:Boolean,duration:Me(ye),swipeable:Boolean,lazyRender:Boolean,currentIndex:Me(Number)},emits:["change"],setup(e,{emit:a,slots:t}){const o=l(),n=e=>a("change",e),s=()=>{var a;const l=null==(a=t.default)?void 0:a.call(t);return e.animated||e.swipeable?f(ha,{ref:o,loop:!1,class:ba("track"),duration:1e3*+e.duration,touchable:e.swipeable,lazyRender:e.lazyRender,showIndicators:!1,onChange:n},{default:()=>[l]}):l},r=a=>{const l=o.value;l&&l.state.active!==a&&l.swipeTo(a,{immediate:!e.inited})};return i((()=>e.currentIndex),r),U((()=>{r(e.currentIndex)})),Ue({swipeRef:o}),()=>f("div",{class:ba("content",{animated:e.animated||e.swipeable})},[s()])}});const[xa,_a]=he("tabs"),ka={type:be("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:we(0),duration:we(.3),animated:Boolean,ellipsis:qe,swipeable:Boolean,scrollspy:Boolean,offsetTop:we(0),background:String,lazyRender:qe,showHeader:qe,lineWidth:ye,lineHeight:ye,beforeChange:Function,swipeThreshold:we(5),titleActiveColor:String,titleInactiveColor:String},Ia=Symbol(xa);var Sa=N({name:xa,props:ka,emits:["change","scroll","rendered","clickTab","update:active"],setup(e,{emit:a,slots:t}){let o,n,s,r,u;const d=l(),c=l(),v=l(),p=l(),m=ra(),g=xe(d),[h,y]=function(){const e=l([]),a=[];return T((()=>{e.value=[]})),[e,l=>(a[l]||(a[l]=a=>{e.value[l]=a}),a[l])]}(),{children:b,linkChildren:w}=je(Ia),x=q({inited:!1,position:"",lineStyle:{},currentIndex:-1}),_=B((()=>b.length>+e.swipeThreshold||!e.ellipsis||e.shrink)),k=B((()=>({borderColor:e.color,background:e.background}))),I=(e,a)=>{var l;return null!=(l=e.name)?l:a},S=B((()=>{const e=b[x.currentIndex];if(e)return I(e,x.currentIndex)})),C=B((()=>_e(e.offsetTop))),$=B((()=>e.sticky?C.value+o:0)),z=a=>{const l=c.value,t=h.value;if(!(_.value&&l&&t&&t[x.currentIndex]))return;const o=t[x.currentIndex].$el,i=o.offsetLeft-(l.offsetWidth-o.offsetWidth)/2;r&&r(),r=function(e,a,l){let t,o=0;const i=e.scrollLeft,n=0===l?1:Math.round(1e3*l/16);let s=i;return function l(){s+=(a-i)/n,e.scrollLeft=s,++o<n&&(t=ve(l))}(),function(){ce(t)}}(l,i,a?0:+e.duration)},N=()=>{const a=x.inited;j((()=>{const l=h.value;if(!l||!l[x.currentIndex]||"line"!==e.type||Ce(d.value))return;const t=l[x.currentIndex].$el,{lineWidth:o,lineHeight:i}=e,n=t.offsetLeft+t.offsetWidth/2,s={width:Le(o),backgroundColor:e.color,transform:`translateX(${n}px) translateX(-50%)`};if(a&&(s.transitionDuration=`${e.duration}s`),He(i)){const e=Le(i);s.height=e,s.borderRadius=e}x.lineStyle=s}))},U=(l,t)=>{const o=(e=>{const a=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=a}})(l);if(!He(o))return;const i=b[o],n=I(i,o),r=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,t||z(),N()),n!==e.active&&(a("update:active",n),r&&a("change",n,i.title)),s&&!e.scrollspy&&Pe(Math.ceil(We(d.value)-C.value))},V=(e,a)=>{const l=b.find(((a,l)=>I(a,l)===e)),t=l?b.indexOf(l):0;U(t,a)},O=(a=!1)=>{if(e.scrollspy){const l=b[x.currentIndex].$el;if(l&&g.value){const t=We(l,g.value)-$.value;n=!0,u&&u(),u=function(e,a,l,t){let o,i=pe(e);const n=i<a,s=0===l?1:Math.round(1e3*l/16),r=(a-i)/s;return function l(){i+=r,(n&&i>a||!n&&i<a)&&(i=a),me(e,i),n&&i<a||!n&&i>a?o=ve(l):t&&(o=ve(t))}(),function(){ce(o)}}(g.value,t,a?0:+e.duration,(()=>{n=!1}))}}},R=(l,t,o)=>{const{title:i,disabled:n}=b[t],s=I(b[t],t);n||(Je(e.beforeChange,{args:[s],done:()=>{U(t),O()}}),ea(l)),a("clickTab",{name:s,title:i,event:o,disabled:n})},E=e=>{s=e.isFixed,a("scroll",e)},M=()=>{if("line"===e.type&&b.length)return f("div",{class:_a("line"),style:x.lineStyle},null)},L=()=>{var a,l,o;const{type:i,border:n,sticky:s}=e,r=[f("div",{ref:s?void 0:v,class:[_a("wrap"),{[De]:"line"===i&&n}]},[f("div",{ref:c,role:"tablist",class:_a("nav",[i,{shrink:e.shrink,complete:_.value}]),style:k.value,"aria-orientation":"horizontal"},[null==(a=t["nav-left"])?void 0:a.call(t),b.map((e=>e.renderTitle(R))),M(),null==(l=t["nav-right"])?void 0:l.call(t)])]),null==(o=t["nav-bottom"])?void 0:o.call(t)];return s?f("div",{ref:v},[r]):r},H=()=>{N(),j((()=>{var e,a;z(!0),null==(a=null==(e=p.value)?void 0:e.swipeRef.value)||a.resize()}))};i((()=>[e.color,e.duration,e.lineWidth,e.lineHeight]),N),i($e,H),i((()=>e.active),(e=>{e!==S.value&&V(e)})),i((()=>b.length),(()=>{x.inited&&(V(e.active),N(),j((()=>{z(!0)})))}));return Ue({resize:H,scrollTo:e=>{j((()=>{V(e),O(!0)}))}}),A(N),Ve(N),ge((()=>{V(e.active,!0),j((()=>{x.inited=!0,v.value&&(o=Te(v.value).height),z(!0)}))})),ua(d,N),Se("scroll",(()=>{if(e.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:a}=Te(b[e].$el);if(a>$.value)return 0===e?0:e-1}return b.length-1})();U(e)}}),{target:g,passive:!0}),w({id:m,props:e,setLine:N,scrollable:_,onRendered:(e,l)=>a("rendered",e,l),currentName:S,setTitleRefs:y,scrollIntoView:z}),()=>f("div",{ref:d,class:_a([e.type])},[e.showHeader?e.sticky?f(va,{container:d.value,offsetTop:C.value,onScroll:E},{default:()=>[L()]}):L():null,f(wa,{ref:p,count:b.length,inited:x.inited,animated:e.animated,duration:e.duration,swipeable:e.swipeable,lazyRender:e.lazyRender,currentIndex:x.currentIndex,onChange:U},{default:()=>{var e;return[null==(e=t.default)?void 0:e.call(t)]}})])}});const Ca=Symbol(),[Ta,$a]=he("tab"),za=N({name:Ta,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:ye,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:qe},setup(e,{slots:a}){const l=B((()=>{const a={},{type:l,color:t,disabled:o,isActive:i,activeColor:n,inactiveColor:s}=e;t&&"card"===l&&(a.borderColor=t,o||(i?a.backgroundColor=t:a.color=t));const r=i?n:s;return r&&(a.color=r),a})),t=()=>{const l=f("span",{class:$a("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||He(e.badge)&&""!==e.badge?f(Fe,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[l]}):l};return()=>f("div",{id:e.id,role:"tab",class:[$a([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:l.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[t()])}}),[Na,qa]=he("swipe-item");const Ba=Ne(N({name:Na,setup(e,{slots:a}){let l;const t=q({offset:0,inited:!1,mounted:!1}),{parent:o,index:i}=Xe(ga);if(!o)return;const n=B((()=>{const e={},{vertical:a}=o.props;return o.size.value&&(e[a?"height":"width"]=`${o.size.value}px`),t.offset&&(e.transform=`translate${a?"Y":"X"}(${t.offset}px)`),e})),s=B((()=>{const{loop:e,lazyRender:a}=o.props;if(!a||l)return!0;if(!t.mounted)return!1;const n=o.activeIndicator.value,s=o.count.value-1,r=0===n&&e?s:n-1,u=n===s&&e?0:n+1;return l=i.value===n||i.value===r||i.value===u,l}));return U((()=>{j((()=>{t.mounted=!0}))})),Ue({setOffset:e=>{t.offset=e}}),()=>{var e;return f("div",{class:qa(),style:n.value},[s.value?null==(e=a.default)?void 0:e.call(a):null])}}})),[ja,Ua]=he("tab");const Aa=Ne(N({name:ja,props:ke({},aa,{dot:Boolean,name:ye,badge:ye,title:String,disabled:Boolean,titleClass:Ze,titleStyle:[String,Object],showZeroBadge:qe}),setup(e,{slots:a}){const t=ra(),o=l(!1),n=C(),{parent:s,index:r}=Xe(Ia);if(!s)return;const u=()=>{var a;return null!=(a=e.name)?a:r.value},d=B((()=>{const a=u()===s.currentName.value;return a&&!o.value&&(o.value=!0,s.props.lazyRender&&j((()=>{s.onRendered(u(),e.title)}))),a})),c=l(""),v=l("");V((()=>{const{titleClass:a,titleStyle:l}=e;c.value=a?O(a):"",v.value=l&&"string"!=typeof l?R(E(l)):l}));const p=l(!d.value);return i(d,(e=>{e?p.value=!1:Oe((()=>{p.value=!0}))})),i((()=>e.title),(()=>{s.setLine(),s.scrollIntoView()})),M(Ca,d),Ue({id:t,renderTitle:l=>f(za,D({key:t,id:`${s.id}-${r.value}`,ref:s.setTitleRefs(r.value),style:v.value,class:c.value,isActive:d.value,controls:t,scrollable:s.scrollable.value,activeColor:s.props.titleActiveColor,inactiveColor:s.props.titleInactiveColor,onClick:e=>l(n.proxy,r.value,e)},Ye(s.props,["type","color","shrink"]),Ye(e,["dot","badge","title","disabled","showZeroBadge"])),{title:a.title})}),()=>{var e;const l=`${s.id}-${r.value}`,{animated:i,swipeable:n,scrollspy:u,lazyRender:c}=s.props;if(!a.default&&!i)return;const v=u||d.value;if(i||n)return f(Ba,{id:t,role:"tabpanel",class:Ua("panel-wrapper",{inactive:p.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":l,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[f("div",{class:Ua("panel")},[null==(e=a.default)?void 0:e.call(a)])]}});const m=o.value||u||!c?null==(e=a.default)?void 0:e.call(a):null;return L(f("div",{id:t,role:"tabpanel",class:Ua("panel"),tabindex:v?0:-1,"aria-labelledby":l,"data-allow-mismatch":"attribute"},[m]),[[H,v]])}}})),Va=Ne(Sa),Oa={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},Ra={class:"pc_container",style:{display:"flex"}},Ea={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%"}},Ma={class:"p-3",style:{display:"flex","justify-content":"space-between"}},La={class:"pc_right bg-[#fff]"},Ha={id:"typing-area"},Da={key:0,class:"decContaniner nop bg-[#fff]"},Pa={key:0,class:"img_box"},Wa=["src"],Ja={key:1,class:"icon"},Fa={class:"process_text label_width"},Xa={key:0,class:"process"},Za={key:0,class:"img_box"},Ya=["src"],Ga={key:1,class:"icon"},Ka={class:"process"},Qa={class:"process_text"},el={key:2},al=["src"],ll=["src"],tl={class:"mobile_container"},ol={class:"p-3",style:{display:"flex","justify-content":"space-between"}},il={class:"mobile_right"},nl={id:"typing-area"},sl={key:0,class:"decContaniner nop bg-[#fff]"},rl={key:0,class:"img_box"},ul=["src"],dl={key:1,class:"icon"},cl={class:"process_text label_width"},vl={key:0,class:"img_box"},pl=["src"],ml={key:1,class:"icon"},fl={class:"process"},gl={class:"process_text"},hl={key:2},yl=P({__name:"index",props:{currentItem:{type:Object,default:()=>{}}},setup(g){var b;const w=g,x=e("loading.png"),_=e("copy.png"),S=q({}),C=o(),T={},$=l([]),z=l(t.get("userInfo")?JSON.parse(t.get("userInfo")):{}),N=l(null),{t:j,locale:A}=W(),V=J(),R=l(!1);let E=l("a"),M=l(!0),L=l("");const H=l(""),D=l(null),P=l(null),ce=l(["1","2"]),ve=l(!1),pe=l(!1),me=l(!1);let fe;const ge=l("");ge.value=decodeURIComponent(`<${null==(b=C.query)?void 0:b.appName}>${la[localStorage.getItem("ai_apps_lang")]}`),F({title:ge,meta:[]});const he=async()=>{var e;await le({appId:C.params.uuid,user:z.value.userName,mode:null==(e=N.value)?void 0:e.mode,task_id:H.value}),setTimeout((()=>{De.abort(),Pe=!0,Be.value=[],ve.value=!1,je.value.length&&je.value.forEach((e=>{e.status=!0})),Ye()}),0)},ye=()=>{me.value=!1},be=async(e,a)=>{var l;let t=te();if(null==(l=z.value)?void 0:l.userId){const l={appUuid:a,priceId:e.priceId,monthNum:e.monthNum};let t=await oe(l);t&&(ie({type:"success",message:j("tool.sS")}),setTimeout((()=>{location.href=t}),1e3))}else t&&"zh-CN"!=t?V.push((isUp,"/login")):window.addLoginDom()};U((async()=>{var e,l;const o=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${o}px`);["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=C.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(l=C.params)?void 0:l.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),A.value=X(),Ne();const{customCss:i,customJs:n}=await Z(C.params.appUuid);t.get("userInfo")&&await ra(),_e(),a(i,n)}));const we=e=>{E.value=e.name},xe=async()=>{me.value=!0},_e=()=>{C.params.uuid&&Y({appId:C.params.uuid,user:z.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&($.value=e.user_input_form,e.user_input_form.forEach((e=>{const a=Object.keys(e)[0],l=e[a].variable;T[l]={label:e[a].label},S[l]=""})))}))},ke=B((()=>!!$.value.length)),Ie=B((()=>$.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),Se=()=>{C.params.uuid&&ne({appId:C.params.uuid,user:z.value.userName}).then((e=>{N.value={...e}}))},Ce=l(!1),Te=l(!1),$e=l(!1),ze=l(!1),Ne=async()=>{var e,a;let l=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=z.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:C.params.uuid,userUuid:null==(a=z.value)?void 0:a.openid}];await G.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",l)},qe=async()=>{var e,a,l;if(t.get("userInfo")){if(await ra(),!(null==(e=w.currentItem)?void 0:e.status))return me.value=!0,!1;if(0!=Ie.value.length||(o=S,Object.values(o).some((e=>e)))){var o;for(let e in S)if(Ie.value.includes(e)&&!S[e])return void ie({message:`${T[e].label}${j("tool.requiredfield")}`,type:"error"});(null==(a=N.value)?void 0:a.mode)&&(["advanced-chat","chat"].includes(null==(l=N.value)?void 0:l.mode)?ie({type:"success",message:j("tool.planning")}):"completion"==N.value.mode?(M.value=!1,setTimeout((()=>{E.value="b"}),1e3),Ze()):(M.value=!1,setTimeout((()=>{E.value="b"}),1e3),Xe()))}else ie({message:`${j("tool.enterquestion")}`,type:"error"})}else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=te();e&&"zh-CN"!=e?V.push("/login"):window.addLoginDom()}},Be=l([]);var je=l([]),Ue=l([]);const Ae=l(""),Ve=l(0),Oe=l(""),Re=l(!1),Ee=l(!1);let Me=l(0);const Le=l(!1),He=l(!1);let De,Pe=!1,We=!1;i(je,(()=>{Je()}),{deep:!0});const Je=()=>{Me.value<je.value.length&&(Ue.value.push(je.value[Me.value]),Me.value++,setTimeout(Je,1e3))},Fe=()=>{Ve.value<Ae.value.length?(Re.value=!0,Oe.value+=Ae.value.charAt(Ve.value),Ve.value++,setTimeout(Fe,20)):(He.value=!1,Re.value=!1,$e.value=!0,Ye())},Xe=async()=>{pe.value=!0,ve.value=!0,L.value="",je.value=[],Ue.value=[],Me.value=0,Oe.value="",Ae.value="",Be.value=[],Le.value=!1,Pe=!1,Ve.value=0,De=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run?locale=zh`;0,Ce.value=!0,Te.value=!0,ze.value=!1,await ta(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:C.params.uuid,user:z.value.userName,inputs:{...S,outputLanguage:S.outputLanguage?S.outputLanguage:"中文"==se()?"简体中文":se()},requestId:crypto.randomUUID(),files:[],response_mode:"streaming",appUuid:C.params.appUuid}),onmessage(e){var a,l,t,o,i,n,s,r,u,d;if(e.data.trim())try{const c=JSON.parse(e.data);if(H.value=c.task_id,c.error)throw new Error(c.error);"智能体推理思维链"==(null==(a=null==c?void 0:c.data)?void 0:a.title)&&"node_finished"===c.event&&(He.value=!0,Ae.value=null==(o=JSON.parse(null==(t=null==(l=null==c?void 0:c.data)?void 0:l.outputs)?void 0:t.text))?void 0:o.text,Fe()),"node_started"!==c.event||Le.value||"开始"==(null==(i=null==c?void 0:c.data)?void 0:i.title)||je.value.push({node_id:null==(n=null==c?void 0:c.data)?void 0:n.node_id,title:null==(s=null==c?void 0:c.data)?void 0:s.title,status:!1}),"error"===c.event&&(5047==c.code&&(Ee.value=!0),Le.value=!0,ve.value=!1,Pe=!0,Te.value=!1,L.value=null==c?void 0:c.message),"node_finished"===c.event&&je.value.forEach((e=>{var a;e.node_id==(null==(a=null==c?void 0:c.data)?void 0:a.node_id)&&(e.status=!0)})),"text_chunk"===c.event&&(R.value=!0,Be.value.push(null==(r=null==c?void 0:c.data)?void 0:r.text),He.value||Ye()),"workflow_started"===c.event&&(Ce.value=!1),"workflow_finished"===c.event&&(Pe=!0,Le.value=!0,ve.value=!1,Te.value=!1,$e.value=!1,R.value||(Be.value.push(null==(d=null==(u=null==c?void 0:c.data)?void 0:u.outputs)?void 0:d.text),He.value||Ye()))}catch(c){oa(c)}},onerror(e){oa(e)},signal:De.signal,openWhenHidden:!0})}catch(e){oa()}},Ze=async()=>{L.value="",Be.value=[],He.value=!1,Pe=!1,De=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages?locale=zh`;0,Ce.value=!0,Te.value=!0,ze.value=!1,await ta(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:C.params.uuid,user:z.value.userName,inputs:{...S,outputLanguage:se()},files:[],response_mode:"streaming",appUuid:C.params.appUuid,requestId:crypto.randomUUID()}),onmessage(e){if(Ce.value=!1,$e.value=!0,e.data.trim())try{const a=JSON.parse(e.data);if(H.value=a.task_id,a.error)throw new Error(a.error);"error"===a.event&&(5047==a.code&&(Ee.value=!0),Pe=!0,L.value=null==a?void 0:a.message,$e.value=!1,Te.value=!1),"message"===a.event&&(Be.value.push(null==a?void 0:a.answer),He.value||Ye()),"message_end"===a.event&&(Pe=!0,Te.value=!1,$e.value=!1)}catch(a){oa(a)}},onerror(e){oa(e)},signal:De.signal,openWhenHidden:!0})}catch(e){oa()}},Ye=()=>{if(0===Be.value.length)return He.value=!1,We=!0,void ea();He.value=!0;const e=Be.value.shift();aa(e).then((()=>{Ye()}))},ea=()=>{We&&Pe&&(Te.value=!1,$e.value=!1,ze.value=!0)},aa=e=>new Promise((a=>{let l=0;fe=setInterval((()=>{if(l<(null==e?void 0:e.length)){L.value+=e[l++];const a=document.getElementsByClassName("pc_right");a[0].scrollTop=a[0].scrollHeight;const t=document.getElementsByClassName("mobile_right");t[0].scrollTop=t[0].scrollHeight}else clearInterval(fe),a()}),0)})),oa=()=>{setTimeout((()=>{De.abort()}),0),ve.value=!1,Ce.value=!1,$e.value=!1,Te.value=!1,He.value=!1,ie.error(j("tool.accessbusy")),L.value=j("tool.accessbusy")},ia=async()=>{try{await navigator.clipboard.writeText(L.value),ie({type:"success",message:j("tool.copysuccess")})}catch(e){ie(e)}},sa=()=>{for(let e in S)S[e]="";D.value.forEach((e=>{e.updateMessage()})),P.value.forEach((e=>{e.updateMessage()}))},ra=async()=>{if(localStorage.getItem("yudaoToken"))Se();else try{await K({userId:z.value.userId,userName:z.value.userName,realName:z.value.realName,avatar:z.value.avatar,plaintextUserId:z.value.plaintextUserId,mobile:z.value.mobile,email:z.value.email}).then((async e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),Se())}))}catch(e){}};return(e,a)=>{const l=I,t=k,o=re,i=ue,g=Q("v-md-preview"),b=de;return n(),s("div",Oa,[r("div",Ra,[d(ke)?(n(),s(p,{key:0},[r("div",Ea,[(n(!0),s(p,null,m(d($),((a,l)=>(n(),s("div",{class:"flex",key:l},[(n(!0),s(p,null,m(a,((a,l)=>(n(),c(na,{key:a.variable,type:l,label:e.$t(null==a?void 0:a.label),value:d(S)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>d(S)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRef",ref:D},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),r("div",Ma,[f(l,{onClick:sa},{default:v((()=>[h(u(e.$t("tool.clear")),1)])),_:1}),f(l,{onClick:qe,loading:d(Te),type:"primary"},{default:v((()=>[h(u(e.$t("tool.execute")),1)])),_:1},8,["loading"])])]),r("div",La,[r("div",Ha,[d(Ue).length>0||d(Oe)||d(pe)?(n(),s("div",Da,[f(i,{modelValue:d(ce),"onUpdate:modelValue":a[0]||(a[0]=e=>ae(ce)?ce.value=e:null)},{default:v((()=>[f(o,{name:"1"},{title:v((()=>[d(ve)?(n(),s("div",Pa,[r("img",{src:d(x),alt:"loading"},null,8,Wa)])):(n(),s("div",Ja,[f(t,null,{default:v((()=>[f(d(ee))])),_:1})])),h(" "+u(e.$t("tool.execution_progress")),1)])),default:v((()=>[(n(!0),s(p,null,m(d(Ue),((l,t)=>(n(),s("div",{key:t,class:"process"},[r("div",Fa,u(l.title),1),a[5]||(a[5]=h("    ")),r("span",{style:{color:"#36b15e"},class:O(l.status?"":"loading-text")},u(l.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),r("div",null,[d(Oe)?(n(),s("div",Xa)):y("",!0)]),d(Oe)?(n(),c(o,{key:0,name:"2"},{title:v((()=>[d(Re)?(n(),s("div",Za,[r("img",{src:d(x),alt:"loading"},null,8,Ya)])):(n(),s("div",Ga,[f(t,null,{default:v((()=>[f(d(ee))])),_:1})])),h(" "+u(e.$t("tool.reasoning_process")),1)])),default:v((()=>[r("div",Ka,[r("div",Qa,u(d(Oe)),1)])])),_:1})):y("",!0)])),_:1},8,["modelValue"])])):y("",!0),d(L)&&!d(Ee)?(n(),c(g,{key:1,text:d(L),id:"previewMd"},null,8,["text"])):y("",!0),d(Ee)?(n(),s("div",el,[h(u(d(L))+" ",1),f(l,{type:"text",onClick:xe},{default:v((()=>[h(u(e.$t("market.subscribe")),1)])),_:1})])):y("",!0),r("div",null,[d($e)?(n(),s("img",{key:0,src:d(x),alt:"loading",class:"spinner"},null,8,al)):y("",!0),d($e)?(n(),s("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:he},u(e.$t("tool.stopGeneration")),1)):y("",!0),d(ze)?(n(),s("img",{key:2,onClick:ia,src:d(_),alt:"",style:{width:"20px"},class:"copy"},null,8,ll)):y("",!0)])])])],64)):y("",!0)]),r("div",tl,[f(d(Va),{active:d(E),shrink:"","line-width":"20",onClickTab:we},{default:v((()=>[f(d(Aa),{title:"输入",name:"a"},{default:v((()=>[(n(!0),s(p,null,m(d($),((a,l)=>(n(),s("div",{class:"flex",key:l},[(n(!0),s(p,null,m(a,((a,l)=>(n(),c(na,{key:a.variable,type:l,label:e.$t(null==a?void 0:a.label),value:d(S)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>d(S)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRefs",ref:P},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),r("div",ol,[f(l,{onClick:sa},{default:v((()=>a[6]||(a[6]=[h("Clear")]))),_:1}),f(l,{onClick:a[1]||(a[1]=e=>qe()),loading:d(Te),type:"primary"},{default:v((()=>a[7]||(a[7]=[h("Execute")]))),_:1},8,["loading"])])])),_:1}),f(d(Aa),{title:"结果",name:"b",disabled:d(M)},{default:v((()=>[r("div",il,[r("div",nl,[d(je).length>0||d(Oe)?(n(),s("div",sl,[f(i,{modelValue:d(ce),"onUpdate:modelValue":a[2]||(a[2]=e=>ae(ce)?ce.value=e:null)},{default:v((()=>[f(o,{name:"1"},{title:v((()=>[d(ve)?(n(),s("div",rl,[r("img",{src:d(x),alt:"loading"},null,8,ul)])):(n(),s("div",dl,[f(t,null,{default:v((()=>[f(d(ee))])),_:1})])),h(" "+u(e.$t("tool.execution_progress")),1)])),default:v((()=>[(n(!0),s(p,null,m(d(je),((l,t)=>(n(),s("div",{key:t,class:"process"},[r("div",cl,u(l.title),1),a[8]||(a[8]=h("    ")),r("span",{style:{color:"#36b15e"},class:O(l.status?"":"loading-text")},u(l.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),a[9]||(a[9]=r("div",null,[r("div",{class:"process"})],-1)),d(Oe)?(n(),c(o,{key:0,title:"推导过程",name:"2"},{title:v((()=>[d(Re)?(n(),s("div",vl,[r("img",{src:d(x),alt:"loading"},null,8,pl)])):(n(),s("div",ml,[f(t,null,{default:v((()=>[f(d(ee))])),_:1})])),h(" "+u(e.$t("tool.reasoning_process")),1)])),default:v((()=>[r("div",fl,[r("div",gl,u(d(Oe)),1)])])),_:1})):y("",!0)])),_:1},8,["modelValue"])])):y("",!0),d(L)&&!d(Ee)?(n(),c(g,{key:1,text:d(L),id:"previewMd"},null,8,["text"])):y("",!0),d(Ee)?(n(),s("div",hl,[h(u(d(L))+" ",1),f(l,{type:"text",onClick:xe},{default:v((()=>[h(u(e.$t("market.subscribe")),1)])),_:1})])):y("",!0)])])])),_:1},8,["disabled"])])),_:1},8,["active"])]),d(me)?(n(),c(b,{key:0,modelValue:d(me),"onUpdate:modelValue":a[3]||(a[3]=e=>ae(me)?me.value=e:null),class:"payPC","show-close":!1},{default:v((()=>[f(Ge,{userInfo:d(z),appTypes:e.appTypes,currentItem:w.currentItem,onToAgreement:e.toAgreement,onClose:ye,onSubscribe:be},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["modelValue"])):y("",!0),d(me)?(n(),c(d(Qe),{key:1,show:d(me),"onUpdate:show":a[4]||(a[4]=e=>ae(me)?me.value=e:null),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"90%"}},{default:v((()=>[f(Ke,{userInfo:d(z),appTypes:e.appTypes,currentItem:w.currentItem,onToAgreement:e.toAgreement,onClose:ye},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["show"])):y("",!0)])}}},[["__scopeId","data-v-7c1c73c4"]]);export{yl as default};
