/* empty css                  *//* empty css                 */import{s as e}from"./index-631291bc.js";import{r as a,aK as l,c as s,d as t,e as u,f as r,p as i,i as n,l as o,j as v,F as p,y as d,U as m,g as c,aM as f,h as x,a3 as y,B as j,E as h,G as g}from"./index-46a7add2.js";import{s as w}from"./index-7124623e.js";/* empty css                   */const k={class:"pt-10 overflow-auto h-full"},F={class:"flex justify-center my-10"},_={key:0},b=["innerHTML"],A={class:"flex justify-center mt-10"},E={__name:"index",setup(E){const M=a(""),S=a([]),V=a([]),z=a(5),C=()=>{if(a=M.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(a)||!M.value)return y.warning("请输入英文内容");var a;e({text:M.value}).then((e=>{e&&e&&e.data&&(S.value=e.data,S.value=w(S.value),z.value=5,H())}))},H=()=>{let e=[];5==z.value?(e=[0,5],z.value=3):3==z.value?(e=[5,8],z.value=2):2==z.value&&(e=[8,10],z.value=5),V.value=JSON.parse(JSON.stringify(S.value)).slice(e[0],e[1])};return(e,a)=>{const y=j,w=h,E=g,S=l("copy");return s(),t("div",k,[u(y,{modelValue:r(M),"onUpdate:modelValue":a[0]||(a[0]=e=>i(M)?M.value=e:null),rows:8,type:"textarea",placeholder:"请输入不超过250单词的段落","show-word-limit":"",resize:"none",maxlength:250},null,8,["modelValue"]),n("div",F,[u(w,{type:"primary",onClick:C},{default:o((()=>a[1]||(a[1]=[v("改 写")]))),_:1})]),r(V)&&r(V).length?(s(),t("div",_,[(s(!0),t(p,null,d(r(V),((e,a)=>(s(),t("div",{class:"flex justify-center mb-6 relative",key:a},[n("span",{innerHTML:e.textShow,class:"text-[18px]"},null,8,b),m((s(),c(E,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-2 absolute"},{default:o((()=>[u(r(f))])),_:2},1024)),[[S,e.text]])])))),128)),n("div",A,[u(w,{type:"primary",link:"",onClick:H},{default:o((()=>a[2]||(a[2]=[v("换一换")]))),_:1})])])):x("",!0)])}}};export{E as default};
