import{x as e,at as a,Y as s,ag as o,ah as t,au as n,av as l,d as r,e as i,j as c,t as d,i as m,F as u,y as g,ad as h,R as p,k as v}from"https://static.medsci.cn/ai-write/static/index-fe292e2d.js";import{_ as w}from"./_plugin-vue_export-helper-1b428a4d.js";const I={class:"header ms-header-media"},f={class:"ms-header"},k={class:"wrapper"},_={class:"main-menu-placeholder wrapper clearfix",style:{height:"56px",display:"block !important"}},y={id:"main-menu",class:"ms-header-nav"},$={class:"header-top header-user",id:"user-info-header"},S={key:0,class:"change_lang"},C={class:"current_lang"},b={class:"ms-link"},T={class:"ms-dropdown-menu",ref:"menu"},x={class:"new-header-avator-pop",id:"new-header-avator"},L={class:"new-header-bottom",style:{padding:"0"}},A={class:"langUl"},U=["onClick"],M={class:"index-user-img_right"},j={key:1,class:"index-user-img"},D={href:"#"},N={class:"img-area"},q=["src"],E={class:"ms-dropdown-menu"},H={class:"new-header-avator-pop",id:"new-header-avator"},R={class:"new-header-top"},Z={class:"new-header-info"},z=["src"],B={class:"new-header-name"};const F=w({data:()=>({avatar:"",userInfo:null,isIncludeTool:!1,selectedLanguage:"",langs:[]}),watch:{userInfoCookie(e,a){}},computed:{baseUrl:()=>"https://www.medsci.cn/",userInfoCookie:()=>e.get("userInfo")},methods:{changeImg(){this.avatar="https://img.medsci.cn/web/img/user_icon.png"},getCookieUserInfo:()=>e.get("userInfo"),showMenu(){this.$refs.menu.style.display="block"},toggle(e){const a=this.$route.path,s=this.$route.params.lang;if(s){const o=a.replace(`/${s}`,"");this.$router.push(`/${e}${o}`)}else this.$router.push(`/${e}${a}`);window.localStorage.setItem("ai_apps_lang",e),this.selectedLanguage=e,this.$i18n.locale=e,this.$emit("getAppLang",e),"zh-CN"==localStorage.getItem("ai_apps_lang")?this.$emit("isZHChange",!0):this.$emit("isZHChange",!1),this.$refs.menu.style.display="none"},async logout(){e.remove("userInfo",{domain:".medon.com.cn"}),e.remove("userInfo",{domain:".medsci.cn"}),e.remove("userInfo",{domain:"localhost"}),localStorage.removeItem("conversation"),localStorage.removeItem("writeContent");let s=localStorage.getItem("socialType");s&&35==s||s&&36==s?a().then((()=>{localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),location.reload()})).catch((e=>{})):(localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),window.location.origin.includes("medsci.cn")?window.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.location.href:window.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.location.href)},async loginAccount(){let e=await this.getLocationData();e&&"中国"!=e?this.$router.push("/login"):window.addLoginDom()},medsciSearch(){let e=document.getElementById("medsciSearchKeyword").value;e?window.open(`https://www.medsci.cn/search?q=${e}`,"_blank"):window.open("https://search.medsci.cn/","_blank")},async getLocationData(){let e=s("current_location_country",1);return e||await o().then((e=>{t("current_location_country",e)})),e=s("current_location_country",1),e},getAppLangsData(){n().then((e=>{e.map((e=>{e.status||this.langs.push({name:e.value,value:e.remark})}))}))}},mounted(){var a,s;this.getAppLangsData(),this.userInfo=e.get("userInfo")?JSON.parse(e.get("userInfo")):null,this.isIncludeTool=window.location.href.includes("/tool"),setTimeout((()=>{this.selectedLanguage=l()}),0),this.avatar=(null==(a=this.userInfo)?void 0:a.avatar)?null==(s=this.userInfo)?void 0:s.avatar:"https://img.medsci.cn/web/img/user_icon.png"}},[["render",function(e,a,s,o,t,n){var l,v,w,F,G;return r(),i("div",I,[c("div",f,[c("div",k,[c("div",_,[a[7]||(a[7]=c("div",{class:"ms-header-img"},[c("img",{src:"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:""})],-1)),c("div",y,[c("div",$,[c("ul",null,[c("li",{class:"index-user-img index-user-img_left",onMouseover:a[0]||(a[0]=(...e)=>n.showMenu&&n.showMenu(...e)),onClick:a[1]||(a[1]=(...e)=>n.showMenu&&n.showMenu(...e))},[t.isIncludeTool?m("",!0):(r(),i("div",S,[c("span",C,d(null==(l=t.langs.filter((e=>e.value==t.selectedLanguage))[0])?void 0:l.name),1),c("span",b,d(e.$t("market.switch")),1)])),c("div",T,[c("div",x,[c("div",L,[c("div",A,[(r(!0),i(u,null,g(t.langs,(e=>(r(),i("p",{key:e,onClick:h((a=>n.toggle(e.value)),["stop"]),class:p({langItemSelected:e.value===t.selectedLanguage})},d(e.name),11,U)))),128))])])])],512)],32),(null==(v=t.userInfo)?void 0:v.userId)?(r(),i("li",j,[c("a",D,[c("div",N,[c("img",{src:t.avatar,onError:a[3]||(a[3]=(...e)=>n.changeImg&&n.changeImg(...e)),alt:""},null,40,q)])]),c("div",E,[c("div",H,[c("a",{class:"new-header-exit ms-statis","ms-statis":"logout",href:"#",onClick:a[4]||(a[4]=e=>n.logout())},d(e.$t("market.logout")),1),c("div",R,[c("div",Z,[c("img",{class:"new-header-avatar",src:t.avatar,onError:a[5]||(a[5]=(...e)=>n.changeImg&&n.changeImg(...e)),alt:""},null,40,z),c("div",B,[c("span",null,d((null==(w=t.userInfo)?void 0:w.realName)?null==(F=t.userInfo)?void 0:F.realName:null==(G=t.userInfo)?void 0:G.userName),1)])])])])])])):(r(),i(u,{key:0},[c("li",M,[c("a",{href:"javascript: void(0)",class:"ms-link",onClick:a[2]||(a[2]=(...e)=>n.loginAccount&&n.loginAccount(...e))},d(e.$t("market.login")),1)]),a[6]||(a[6]=c("li",null,null,-1))],64))])])])])])])])}],["__scopeId","data-v-0009de79"]]),G={class:"assistant-container"};const J=w({name:"AssistantComponent",data:()=>({}),methods:{}},[["render",function(e,a,s,o,t,n){return r(),i("div",G,a[0]||(a[0]=[c("div",{class:"assistant-icon"},[c("img",{src:"/apps/static/kefu-ae1166e2.png",class:"fas fa-user-astronaut",alt:"客服"})],-1),c("div",{class:"qr-code"},[c("img",{src:"/apps/static/qrcode-4bdb4a15.png",alt:"QR Code"}),v(" 扫码添加小助手 ")],-1)]))}],["__scopeId","data-v-459f35cd"]]);export{F as _,J as c};
