import{a as s,d as e,e as i,j as o,k as l,S as t}from"./index-4b0c90f0.js";const a=s({name:"AssistantComponent",data:()=>({isCollapsed:!1,isQrCodeVisible:!1,isMobile:!1}),mounted(){this.checkMobile(),window.addEventListener("resize",this.checkMobile)},beforeUnmount(){window.removeEventListener("resize",this.checkMobile)},methods:{toggleCollapse(){this.isCollapsed=!this.isCollapsed,this.isCollapsed?setTimeout((()=>{this.isQrCodeVisible=!1}),300):this.isQrCodeVisible=!0},checkMobile(){this.isMobile=window.innerWidth<=768,this.isMobile&&(this.isCollapsed=!0,this.isQrCodeVisible=!1)}}},[["render",function(s,a,d,n,c,r){return e(),i("div",{class:t(["assistant-container",{"is-collapsed":c.isCollapsed}])},[o("div",{class:"assistant-icon",onClick:a[0]||(a[0]=(...s)=>r.toggleCollapse&&r.toggleCollapse(...s))},a[1]||(a[1]=[o("img",{src:"/apps/static/kefu-ae1166e2.png",class:"fas fa-user-astronaut",alt:"客服"},null,-1)])),o("div",{class:t(["qr-code",{"is-visible":!c.isCollapsed&&c.isQrCodeVisible}])},a[2]||(a[2]=[o("img",{src:"/apps/static/qrcode-4bdb4a15.png",alt:"QR Code"},null,-1),l(" 扫码添加小助手 ")]),2)],2)}],["__scopeId","data-v-ee471f14"]]);export{a as c};
