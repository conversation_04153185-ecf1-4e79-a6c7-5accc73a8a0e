import{a as e,E as o,K as t,m as s,n as a,p as n,e as l,V as r,t as i,M as c,q as u,o as d,W as p,B as v,h as m,f as y,F as f,x as g,X as h,J as x,Y as S,N as b,Z as w}from"./use-touch-b0c9fc93.js";import{N as k,ay as z,P as O,f as C,V as I,L as B,M as P,w as L,r as T,J as j,ac as A,az as E,T as N,U as R,n as $,o as D,Q as F,S as H,F as M,al as V}from"./index-9ae28250.js";let J=2e3;const[K,U]=e("config-provider"),Y=Symbol(K),[q,Q]=e("icon");const W=n(k({name:q,props:{dot:Boolean,tag:s("i"),name:String,size:a,badge:a,color:String,badgeProps:Object,classPrefix:String},setup(e,{slots:s}){const a=z(Y,null),n=O((()=>e.classPrefix||(null==a?void 0:a.iconPrefix)||Q()));return()=>{const{tag:a,dot:l,name:r,size:i,badge:c,color:u}=e,d=(e=>null==e?void 0:e.includes("/"))(r);return C(t,I({dot:l,tag:a,class:[n.value,d?"":`${n.value}-${r}`],style:{color:u,fontSize:o(i)},content:c},e.badgeProps),{default:()=>{var e;return[null==(e=s.default)?void 0:e.call(s),d&&C("img",{class:Q("image"),src:r},null)]}})}}})),[X,Z]=e("loading"),G=Array(12).fill(null).map(((e,o)=>C("i",{class:Z("line",String(o+1))},null))),_=C("svg",{class:Z("circular"),viewBox:"25 25 50 50"},[C("circle",{cx:"50",cy:"50",r:"20",fill:"none"},null)]);const ee=n(k({name:X,props:{size:a,type:s("circular"),color:String,vertical:Boolean,textSize:a,textColor:String},setup(e,{slots:t}){const s=O((()=>l({color:e.color},r(e.size)))),a=()=>{const o="spinner"===e.type?G:_;return C("span",{class:Z("spinner",e.type),style:s.value},[t.icon?t.icon():o])},n=()=>{var s;if(t.default)return C("span",{class:Z("text"),style:{fontSize:o(e.textSize),color:null!=(s=e.textColor)?s:e.color}},[t.default()])};return()=>{const{type:o,vertical:t}=e;return C("div",{class:Z([o,{vertical:t}]),"aria-live":"polite","aria-busy":!0},[a(),n()])}}})),oe={show:Boolean,zIndex:a,overlay:i,duration:a,teleport:[String,Object],lockScroll:i,lazyRender:i,beforeClose:Function,overlayStyle:Object,overlayClass:c,transitionAppear:Boolean,closeOnClickOverlay:i},te=Object.keys(oe);let se=0;const ae="van-overflow-hidden";function ne(e){const o=T(!1);return L(e,(e=>{e&&(o.value=e)}),{immediate:!0}),e=>()=>o.value?e():null}const le=()=>{var e;const{scopeId:o}=(null==(e=j())?void 0:e.vnode)||{};return o?{[o]:""}:null},[re,ie]=e("overlay");const ce=n(k({name:re,props:{show:Boolean,zIndex:a,duration:a,className:c,lockScroll:i,lazyRender:i,customStyle:Object,teleport:[String,Object]},setup(e,{slots:o}){const t=T(),s=ne((()=>e.show||!e.lazyRender))((()=>{var s;const a=l(y(e.zIndex),e.customStyle);return f(e.duration)&&(a.animationDuration=`${e.duration}s`),N(C("div",{ref:t,style:a,class:[ie(),e.className]},[null==(s=o.default)?void 0:s.call(o)]),[[R,e.show]])}));return m("touchmove",(o=>{e.lockScroll&&v(o,!0)}),{target:t}),()=>{const o=C(A,{name:"van-fade",appear:!0},{default:s});return e.teleport?C(E,{to:e.teleport},{default:()=>[o]}):o}}})),ue=l({},oe,{round:Boolean,position:s("center"),closeIcon:s("cross"),closeable:Boolean,transition:String,iconPrefix:String,closeOnPopstate:Boolean,closeIconPosition:s("top-right"),safeAreaInsetTop:Boolean,safeAreaInsetBottom:Boolean}),[de,pe]=e("popup");const ve=n(k({name:de,inheritAttrs:!1,props:ue,emits:["open","close","opened","closed","keydown","update:show","clickOverlay","clickCloseIcon"],setup(e,{emit:o,attrs:t,slots:s}){let a,n;const l=T(),r=T(),i=ne((()=>e.show||!e.lazyRender)),c=O((()=>{const o={zIndex:l.value};if(f(e.duration)){o["center"===e.position?"animationDuration":"transitionDuration"]=`${e.duration}s`}return o})),y=()=>{a||(a=!0,l.value=void 0!==e.zIndex?+e.zIndex:++J,o("open"))},b=()=>{a&&x(e.beforeClose,{done(){a=!1,o("close"),o("update:show",!1)}})},w=t=>{o("clickOverlay",t),e.closeOnClickOverlay&&b()},k=()=>{if(e.overlay)return C(ce,I({show:e.show,class:e.overlayClass,zIndex:l.value,duration:e.duration,customStyle:e.overlayStyle,role:e.closeOnClickOverlay?"button":void 0,tabindex:e.closeOnClickOverlay?0:void 0},le(),{onClick:w}),{default:s["overlay-content"]})},z=e=>{o("clickCloseIcon",e),b()},j=()=>{if(e.closeable)return C(W,{role:"button",tabindex:0,name:e.closeIcon,class:[pe("close-icon",e.closeIconPosition),S],classPrefix:e.iconPrefix,onClick:z},null)};let V;const K=()=>{V&&clearTimeout(V),V=setTimeout((()=>{o("opened")}))},U=()=>o("closed"),Y=e=>o("keydown",e),q=i((()=>{var o;const{round:a,position:n,safeAreaInsetTop:l,safeAreaInsetBottom:i}=e;return N(C("div",I({ref:r,style:c.value,role:"dialog",tabindex:0,class:[pe({round:a,[n]:n}),{"van-safe-area-top":l,"van-safe-area-bottom":i}],onKeydown:Y},t,le()),[null==(o=s.default)?void 0:o.call(s),j()]),[[R,e.show]])})),Q=()=>{const{position:o,transition:t,transitionAppear:s}=e;return C(A,{name:t||("center"===o?"van-fade":`van-popup-slide-${o}`),appear:s,onAfterEnter:K,onAfterLeave:U},{default:q})};return L((()=>e.show),(e=>{e&&!a&&(y(),0===t.tabindex&&$((()=>{var e;null==(e=r.value)||e.focus()}))),!e&&a&&(a=!1,o("close"))})),g({popupRef:r}),function(e,o){const t=u(),s=o=>{t.move(o);const s=t.deltaY.value>0?"10":"01",a=p(o.target,e.value),{scrollHeight:n,offsetHeight:l,scrollTop:r}=a;let i="11";0===r?i=l>=n?"00":"01":r+l>=n&&(i="10"),"11"===i||!t.isVertical()||parseInt(i,2)&parseInt(s,2)||v(o,!0)},a=()=>{document.addEventListener("touchstart",t.start),document.addEventListener("touchmove",s,{passive:!1}),se||document.body.classList.add(ae),se++},n=()=>{se&&(document.removeEventListener("touchstart",t.start),document.removeEventListener("touchmove",s),se--,se||document.body.classList.remove(ae))},l=()=>o()&&n();d((()=>o()&&a())),B(l),P(l),L(o,(e=>{e?a():n()}))}(r,(()=>e.show&&e.lockScroll)),m("popstate",(()=>{e.closeOnPopstate&&(b(),n=!1)})),D((()=>{e.show&&y()})),F((()=>{n&&(o("update:show",!0),n=!1)})),B((()=>{e.show&&e.teleport&&(b(),n=!0)})),H(h,(()=>e.show)),()=>e.teleport?C(E,{to:e.teleport},{default:()=>[k(),Q()]}):C(M,null,[k(),Q()])}}));let me=0;const[ye,fe]=e("toast"),ge=["show","overlay","teleport","transition","overlayClass","overlayStyle","closeOnClickOverlay","zIndex"];var he=k({name:ye,props:{icon:String,show:Boolean,type:s("text"),overlay:Boolean,message:a,iconSize:a,duration:w(2e3),position:s("middle"),teleport:[String,Object],wordBreak:String,className:c,iconPrefix:String,transition:s("van-fade"),loadingType:String,forbidClick:Boolean,overlayClass:c,overlayStyle:Object,closeOnClick:Boolean,closeOnClickOverlay:Boolean,zIndex:a},emits:["update:show"],setup(e,{emit:o,slots:t}){let s,a=!1;const n=()=>{const o=e.show&&e.forbidClick;a!==o&&(a=o,a?(me||document.body.classList.add("van-toast--unclickable"),me++):me&&(me--,me||document.body.classList.remove("van-toast--unclickable")))},l=e=>o("update:show",e),r=()=>{e.closeOnClick&&l(!1)},i=()=>clearTimeout(s),c=()=>{const{icon:o,type:t,iconSize:s,iconPrefix:a,loadingType:n}=e;return o||"success"===t||"fail"===t?C(W,{name:o||t,size:s,class:fe("icon"),classPrefix:a},null):"loading"===t?C(ee,{class:fe("loading"),size:s,type:n},null):void 0},u=()=>{const{type:o,message:s}=e;return t.message?C("div",{class:fe("text")},[t.message()]):f(s)&&""!==s?"html"===o?C("div",{key:0,class:fe("text"),innerHTML:String(s)},null):C("div",{class:fe("text")},[s]):void 0};return L((()=>[e.show,e.forbidClick]),n),L((()=>[e.show,e.type,e.message,e.duration]),(()=>{i(),e.show&&e.duration>0&&(s=setTimeout((()=>{l(!1)}),e.duration))})),D(n),V(n),()=>C(ve,I({class:[fe([e.position,"normal"===e.wordBreak?"break-normal":e.wordBreak,{[e.type]:!e.icon}]),e.className],lockScroll:!1,onClick:r,onClosed:i,"onUpdate:show":l},b(e,ge)),{default:()=>[c(),u()]})}});const xe=n(he);export{W as I,ee as L,ve as P,xe as T,te as a,oe as p,he as s};
