/* empty css                  *//* empty css                     *//* empty css                 */import{a as e,u as o,V as s,r as l,N as t,x as a,o as n,aA as i,c,d as r,i as d,t as u,e as p,l as m,j as g,f,aB as I,aC as _,B as v,aD as k,E as h,aE as B,aF as b,ae as y,af as x}from"./index-0ee7a7ea.js";import{_ as w}from"./_plugin-vue_export-helper-1b428a4d.js";const S=e=>(y("data-v-ee86b908"),e=e(),x(),e),T={class:"bg-background text-foreground antialiased min-h-screen flex items-center justify-center"},U={class:"cl-rootBox cl-signUp-root justify-center"},j={class:"cl-cardBox cl-signUp-start"},$={class:"cl-card cl-signUp-start"},A={class:"cl-header"},N={class:"cl-headerTitle"},O={class:"cl-headerSubtitle"},C={class:"cl-main"},J={class:"cl-socialButtonsRoot"},V={class:"cl-socialButtons"},z={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},q={class:"cl-socialButtonsBlockButton-d"},L=S((()=>d("span",null,[d("img",{src:"https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1))),P={class:"cl-socialButtons"},R={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},E={class:"cl-socialButtonsBlockButton-d"},F=S((()=>d("span",null,[d("img",{src:"https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1))),G={class:"cl-dividerRow"},M=S((()=>d("div",{class:"cl-dividerLine"},null,-1))),W={class:"cl-dividerText"},Z=S((()=>d("div",{class:"cl-dividerLine"},null,-1))),H={class:"cl-socialButtonsRoot"},D={class:"cl-internal-1pnppin"},Q=S((()=>d("div",{id:"clerk-captcha",class:"cl-internal-3s7k9k"},null,-1))),K={class:"cl-internal-742eeh"},X={class:"cl-internal-2iusy0"},Y=S((()=>d("svg",{class:"cl-buttonArrowIcon cl-internal-1c4ikgf"},[d("path",{fill:"currentColor",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"m7.25 5-3.5-2.25v4.5L7.25 5Z"})],-1))),ee={class:"cl-footer cl-internal-4x6jej"},oe={class:"cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70"},se={class:"cl-footerActionText cl-internal-kyvqj0","data-localization-key":"signUp.start.actionText"},le=w({__name:"index",setup(y){var x;const{t:w}=e(),S=o(),le=s(),te=S.params.socialType,ae=S.query.authCode,ne=S.query.authState,ie=l({email:"",password:""}),ce=(null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location?void 0:location.origin.includes("medsci.cn")),re=l(),de=t({password:[{required:!0,message:w("tool.password_cannot_be_empty"),trigger:"blur"},{min:8,max:20,message:w("tool.pwdLength"),trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,message:w("tool.password_includes_uppercase_lowercase_numbers_and_symbols"),trigger:"blur"}],email:[{required:!0,message:w("tool.email_address_cannot_be_empty"),trigger:"blur"},{type:"email",message:w("tool.please_enter_a_valid_email_address"),trigger:"blur"}]}),ue=a.get("userInfo")?null==(x=JSON.parse(a.get("userInfo")))?void 0:x.userId:"",pe=e=>{I(e).then((e=>{window.location.href=e}))};return n((()=>{ue?le.push("/"):te&&ae&&ne&&i(te,ae,ne).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?a.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?a.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):a.set("userInfo",JSON.stringify(e.userInfo),{expires:365}),le.push("/"))}))})),(e,o)=>{const s=v,l=k,t=h,n=B,i=b;return c(),r("div",T,[d("div",U,[d("div",j,[d("div",$,[d("div",A,[d("div",null,[d("h1",N,u(e.$t("tool.login_to_MedSci_xAI")),1),d("p",O,u(e.$t("tool.welcome_back_please_login_to_continue")),1)])]),d("div",C,[d("div",J,[d("div",V,[d("button",z,[d("span",q,[L,d("span",{class:"cl-socialButtonsBlockButtonText",onClick:o[0]||(o[0]=e=>pe(35))},u(e.$t("tool.continue_with_google")),1)])])]),d("div",P,[d("button",R,[d("span",E,[F,d("span",{class:"cl-socialButtonsBlockButtonText",onClick:o[1]||(o[1]=e=>pe(36))},u(e.$t("tool.continue_with_facebook")),1)])])])]),d("div",G,[M,d("p",W,u(e.$t("tool.or")),1),Z]),d("div",H,[p(n,{ref_key:"ruleFormRef",ref:re,style:{"max-width":"600px"},model:ie.value,rules:de,"label-width":"auto",class:"demo-ruleForm","label-position":"left",size:e.formSize,"status-icon":""},{default:m((()=>[p(l,{label:e.$t("tool.email"),prop:"email"},{default:m((()=>[p(s,{modelValue:ie.value.email,"onUpdate:modelValue":o[2]||(o[2]=e=>ie.value.email=e)},null,8,["modelValue"])])),_:1},8,["label"]),p(l,{label:e.$t("tool.password"),prop:"password",style:{"padding-bottom":"20px"}},{default:m((()=>[p(s,{modelValue:ie.value.password,"onUpdate:modelValue":o[3]||(o[3]=e=>ie.value.password=e),"show-password":"true"},null,8,["modelValue"])])),_:1},8,["label"]),p(l,null,{default:m((()=>[d("div",D,[Q,d("div",K,[p(t,{class:"cl-formButtonPrimary cl-button cl-internal-ttumny",onClick:o[4]||(o[4]=e=>(async e=>{e&&await e.validate(((e,o)=>{e&&_(ie.value).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?a.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?a.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):a.set("userInfo",JSON.stringify(e.userInfo),{expires:365}),le.push("/"))}))}))})(re.value))},{default:m((()=>[d("span",X,[g(u(e.$t("tool.continue")),1),Y])])),_:1})])])])),_:1})])),_:1},8,["model","rules","size"])])])]),d("div",ee,[d("div",oe,[d("span",se,u(e.$t("tool.no_account_yet")),1),p(i,{href:f(ce)?"/apps/sign-up":"/sign-up",class:"cl-footerActionLink"},{default:m((()=>[g(u(e.$t("tool.signUp")),1)])),_:1},8,["href"])])])])])])}}},[["__scopeId","data-v-ee86b908"]]);export{le as default};
