/* empty css                  */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                   *//* empty css                *//* empty css                 */import{m as e}from"./index-e22a5c57.js";import{a as l,r as t,aP as s,d as a,e as r,j as i,f as o,g as d,q as c,a1 as n,F as p,z as x,t as u,h as v,T as m,m as f,aQ as b,aU as g,i as h,a5 as y,C as j,H as w,aT as k,aV as _,a9 as z,aa as T,aW as A}from"./index-07c76d0c.js";import{s as C}from"./index-d19a8d4a.js";/* empty css                   */const V=e=>(z("data-v-0ac38a84"),e=e(),T(),e),H={class:"p-4 h-full"},L={class:"bg-white box h-full flex"},M={class:"w-[50%] flex flex-col"},U=V((()=>i("div",{class:"text-[16px] px-6 font-bold text-gray-600 h-[50px] flex items-center border border-solid border-gray-300 border-top-0 border-left-0 border-right-0"}," 输入摘要 ",-1))),q={class:"flex justify-end mb-4 mr-4"},F={class:"flex-1 flex flex-col h-full overflow-hidden"},I=V((()=>i("div",{class:"text-[16px] px-6 font-bold text-gray-600 min-h-[50px] flex items-center border border-solid border-gray-300 border-top-0 border-left-0 border-right-0"}," 生成5个Title ",-1))),P={class:"border-r flex-1 p-4 flex flex-col overflow-hidden"},Q={class:"flex items-center mb-4"},W=["onClick"],Z={key:1,class:"py-4 flex-1 overflow-auto"},$={class:"mb-4"},B={class:"mb-2 relative"},D={class:"text-[#0071bc]"},E={class:"text-gray-500 text-[12px]"},G={class:"text-gray-500 text-[12px] flex items-center mb-4"},J=V((()=>i("span",null,"参考文献（显示5条）",-1))),K={class:"flex"},N={class:"mr-2"},O={class:"w-[22px] h-[22px] flex items-center justify-center px-1 bg-[#4d73be] text-white rounded-md"},R={class:"mb-2 relative"},S=["innerHTML"],X={class:"text-gray-500 text-[12px]"},Y=l({__name:"index",setup(l){const z=t(""),T=t(null),V=t(1),Y=t([]),ee=t(null),le=e=>{T.value=e,V.value=1,te()},te=()=>{if(!z.value)return y.warning("请输入摘要");e("recommend-title",{text:z.value,mode:T.value}).then((e=>{e&&e.data&&(Y.value=e.data,Y.value=C(Y.value),ee.value=Y.value[0],ee.value.recArticles=C(ee.value.recArticles))}))};return(e,l)=>{const t=j,y=A,C=w,te=k,se=_,ae=s("copy");return a(),r("div",H,[i("div",L,[i("div",M,[U,o(t,{class:"p-6 flex-1",modelValue:d(z),"onUpdate:modelValue":l[0]||(l[0]=e=>c(z)?z.value=e:null),type:"textarea",placeholder:"","show-word-limit":"",resize:"none"},null,8,["modelValue"]),i("div",q,[i("div",{class:n(["text-white font-bold cursor-pointer rounded-md px-4 py-1 mr-4","classics"==d(T)?"bg-[#499557]":"bg-gray-400"]),onClick:l[1]||(l[1]=e=>le("classics"))}," 经典 ",2),i("div",{class:n(["text-white font-bold cursor-pointer rounded-md px-4 py-1","light"==d(T)?"bg-[#499557]":"bg-gray-400"]),onClick:l[2]||(l[2]=e=>le("light"))}," 眼前一亮 ",2)])]),i("div",F,[I,i("div",P,[i("div",Q,[(a(!0),r(p,null,x(d(Y),((e,l)=>(a(),r("div",{class:n(["cursor-pointer px-4 text-white mr-4",l+1==d(V)?"bg-[#4d73be]":"bg-gray-400"]),key:l,onClick:t=>((e,l)=>{V.value=l+1,ee.value=e})(e,l)},u(`Title${l+1}`),11,W)))),128))]),d(ee)?(a(),r("div",Z,[i("div",$,[i("div",B,[i("span",D,u(d(ee).title),1),m((a(),v(C,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-4 absolute top-1"},{default:f((()=>[o(d(b))])),_:1})),[[ae,d(ee).title]])]),i("div",E,u(d(ee).titleZh),1),o(te)]),i("div",null,[i("div",G,[J,o(se,{effect:"dark",content:"以上Title由系统自动参照海量高分文献的主题内容生成，此处随机显示5条。",placement:"top"},{default:f((()=>[o(C,{color:"#999",size:"16",class:"cursor-pointer"},{default:f((()=>[o(d(g))])),_:1})])),_:1})]),(a(!0),r(p,null,x(d(ee).recArticles,((e,l)=>(a(),r("div",{key:l},[i("div",K,[i("div",N,[i("div",O,u(l+1),1)]),i("div",null,[i("div",R,[i("span",{innerHTML:e.title},null,8,S),m((a(),v(C,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-4 absolute top-1"},{default:f((()=>[o(d(b))])),_:2},1024)),[[ae,e.title.replace(/<[^>]+>/g,"")]])]),i("div",X,u(e.zh_title),1)])]),l+1<d(ee).recArticles.length?(a(),v(te,{key:0})):h("",!0)])))),128))])])):(a(),v(y,{key:0,description:"暂无数据"}))])])])])}}},[["__scopeId","data-v-0ac38a84"]]);export{Y as default};
