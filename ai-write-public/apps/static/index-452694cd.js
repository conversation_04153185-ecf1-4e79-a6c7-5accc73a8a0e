import{a as e,x as a,au as s,$ as o,av as n,d as t,e as l,j as i,t as r,i as c,F as d,y as u,ai as m,S as h}from"./index-77fb797e.js";const g={class:"header ms-header-media"},p={class:"ms-header"},v={class:"wrapper"},w={class:"main-menu-placeholder wrapper clearfix",style:{height:"56px",display:"block !important"}},f={class:"ms-header-img"},I=["href"],y={id:"main-menu",class:"ms-header-nav"},k={class:"header-top header-user",id:"user-info-header"},$={key:0,class:"change_lang"},S={class:"current_lang"},M={class:"ms-link"},_={class:"ms-dropdown-menu",ref:"menu"},b={class:"new-header-avator-pop",id:"new-header-avator"},C={class:"new-header-bottom",style:{padding:"0"}},T={class:"langUl"},x=["onClick"],U={class:"index-user-img_right"},L={href:"#"},A={class:"img-area"},N=["src"],j={class:"ms-dropdown-menu",ref:"menu1"},E={class:"new-header-avator-pop",id:"new-header-avator"},D={class:"new-header-top"},z={class:"new-header-info"},H=["src"],O={class:"new-header-name"};const Z=e({data:()=>({avatar:"",userInfo:null,isIncludeTool:!1,selectedLanguage:"",langs:[],hrefUrl:""}),watch:{userInfoCookie(e,a){}},computed:{baseUrl:()=>"https://www.medsci.cn/",userInfoCookie:()=>a.get("userInfo")},methods:{changeImg(){this.avatar="https://img.medsci.cn/web/img/user_icon.png"},getCookieUserInfo:()=>a.get("userInfo"),showMenu(){this.$refs.menu.style.display="block"},hideMenu(){this.$refs.menu.style.display="none"},showMenu1(){this.$refs.menu1.style.display="block"},hideMenu1(){this.$refs.menu1.style.display="none"},toggle(e){const a=this.$route.path,s=this.$route.params.lang;if(s){const o=a.replace(`/${s}`,"");this.$router.push(`/${e}${o}`)}else this.$router.push(`/${e}${a}`);window.localStorage.setItem("ai_apps_lang",e),this.selectedLanguage=e,this.$i18n.locale=e,this.$emit("getAppLang",e),"zh-CN"==localStorage.getItem("ai_apps_lang")?this.$emit("isZHChange",!0):this.$emit("isZHChange",!1),this.$refs.menu.style.display="none"},async logout(){a.remove("userInfo",{domain:".medon.com.cn"}),a.remove("userInfo",{domain:".medsci.cn"}),a.remove("userInfo",{domain:"localhost"}),localStorage.removeItem("conversation"),Object.keys(localStorage).forEach((e=>{e.includes("writeContent")&&localStorage.removeItem(e)}));let e=localStorage.getItem("socialType");e&&35==e||e&&36==e?s().then((()=>{localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),location.reload()})).catch((e=>{})):(localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),window.location.origin.includes("medsci.cn")?window.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.location.href:window.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.location.href)},async loginAccount(){let e=o();e&&"zh-CN"!=e?this.$router.push("/login"):window.addLoginDom()},medsciSearch(){let e=document.getElementById("medsciSearchKeyword").value;e?window.open(`https://www.medsci.cn/search?q=${e}`,"_blank"):window.open("https://search.medsci.cn/","_blank")},getAppLangsData(){n().then((e=>{e.map((e=>{e.status||this.langs.push({name:e.value,value:e.remark})}))}))}},mounted(){var e,s;this.hrefUrl="https://ai.medon.com.cn",this.getAppLangsData(),this.userInfo=a.get("userInfo")?JSON.parse(a.get("userInfo")):null,this.isIncludeTool=window.location.href.includes("/tool"),setTimeout((()=>{this.selectedLanguage=o()}),0),this.avatar=(null==(e=this.userInfo)?void 0:e.avatar)?null==(s=this.userInfo)?void 0:s.avatar:"https://img.medsci.cn/web/img/user_icon.png"}},[["render",function(e,a,s,o,n,Z){var q,B,F,G,J;return t(),l("div",g,[i("div",p,[i("div",v,[i("div",w,[i("div",f,[i("a",{href:n.hrefUrl},a[9]||(a[9]=[i("img",{src:"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:""},null,-1)]),8,I)]),i("div",y,[i("div",k,[i("ul",null,[i("li",{class:"index-user-img index-user-img_left",onMouseover:a[0]||(a[0]=(...e)=>Z.showMenu&&Z.showMenu(...e)),onMouseout:a[1]||(a[1]=(...e)=>Z.hideMenu&&Z.hideMenu(...e)),onClick:a[2]||(a[2]=(...e)=>Z.showMenu&&Z.showMenu(...e))},[n.isIncludeTool?c("",!0):(t(),l("div",$,[i("span",S,r(null==(q=n.langs.filter((e=>e.value==n.selectedLanguage))[0])?void 0:q.name),1),i("span",M,r(e.$t("market.switch")),1)])),i("div",_,[i("div",b,[i("div",C,[i("div",T,[(t(!0),l(d,null,u(n.langs,(e=>(t(),l("p",{key:e,onClick:m((a=>Z.toggle(e.value)),["stop"]),class:h({langItemSelected:e.value===n.selectedLanguage})},r(e.name),11,x)))),128))])])])],512)],32),(null==(B=n.userInfo)?void 0:B.userId)?(t(),l("li",{key:1,class:"index-user-img",onMouseover:a[7]||(a[7]=(...e)=>Z.showMenu1&&Z.showMenu1(...e)),onMouseout:a[8]||(a[8]=(...e)=>Z.hideMenu1&&Z.hideMenu1(...e))},[i("a",L,[i("div",A,[i("img",{src:n.avatar,onError:a[4]||(a[4]=(...e)=>Z.changeImg&&Z.changeImg(...e)),alt:""},null,40,N)])]),i("div",j,[i("div",E,[i("a",{class:"new-header-exit ms-statis","ms-statis":"logout",href:"#",onClick:a[5]||(a[5]=e=>Z.logout())},r(e.$t("market.logout")),1),i("div",D,[i("div",z,[i("img",{class:"new-header-avatar",src:n.avatar,onError:a[6]||(a[6]=(...e)=>Z.changeImg&&Z.changeImg(...e)),alt:""},null,40,H),i("div",O,[i("span",null,r((null==(F=n.userInfo)?void 0:F.realName)?null==(G=n.userInfo)?void 0:G.realName:null==(J=n.userInfo)?void 0:J.userName),1)])])])])],512)],32)):(t(),l(d,{key:0},[i("li",U,[i("a",{href:"javascript: void(0)",class:"ms-link",onClick:a[3]||(a[3]=(...e)=>Z.loginAccount&&Z.loginAccount(...e))},r(e.$t("market.login")),1)]),a[10]||(a[10]=i("li",null,null,-1))],64))])])])])])])])}],["__scopeId","data-v-659859f8"]]);export{Z as _};
