import{a as e,_ as l}from"./Editor-f59bdac0.js";/* empty css                  *//* empty css                  *//* empty css                 */import{m as a}from"./index-e646eac4.js";import{s as t}from"./index-8adc767b.js";/* empty css                   */import{a as s,r as o,N as n,o as i,d as r,e as d,j as p,S as u,g as m,f as c,p as x,m as f,k as v,F as g,y as h,t as y,i as b,h as j,q as k,a6 as w,B as V,E as z,aj as S}from"./index-77fb797e.js";/* empty css                  *//* empty css                  */const T={class:"flex relative h-full overflow-auto"},C={class:"w-[58%]"},N={class:"flex items-center my-4"},_={class:"flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2"},U={class:"flex items-center my-4"},H={class:"flex items-center mr-4"},L={class:"mr-2"},M=["innerHTML","onClick"],q=["innerHTML"],$={class:"flex-1 ml-4 box-right"},E=s({__name:"index",setup(s){const E=o("supplement data needed"),W=o(""),B=o(1),F=o(!0),I=o({}),K=o("请输入审稿人的意见关键词或短句（中/英）"),P=o(1),A=n({pageNo:1,pageSize:20}),D=o(344),G=e=>{E.value="",I.value={},P.value=e,K.value=1==e?"请输入审稿人的意见关键词或短句（中/英）":"请输入回复关键词或短句（中/英）"},J=e=>{if(!E.value)return w.warning("请输入文本");1==e&&(A.pageNo=1),a("sentence",{searchKey:E.value,page:A.pageNo-1,size:A.pageSize}).then((e=>{e&&e.data&&(I.value=e.data,I.value.content=t(I.value.content))}))},O=()=>{B.value++};return i((()=>{J(),D.value=document.querySelector(".box-right").offsetWidth,window.onresize=()=>{D.value=document.querySelector(".box-right").offsetWidth}})),(a,t)=>{const s=V,o=z,n=S,i=e,w=l;return r(),d("div",T,[p("div",C,[p("div",N,[p("div",{class:u(["px-4 py-1 rounded mr-4 font-bold cursor-pointer",1==m(P)?"bg-[#409eff] text-white":"bg-gray-200 text-[#606266]"]),onClick:t[0]||(t[0]=e=>G(1))}," 审稿意见 ",2),p("div",{class:u(["px-4 py-1 rounded mr-4 font-bold cursor-pointer",2==m(P)?"bg-[#409eff] text-white":"bg-gray-200 text-[#606266]"]),onClick:t[1]||(t[1]=e=>G(2))}," 回复内容 ",2)]),p("div",_,[c(s,{class:"h-full !text-[24px]",modelValue:m(E),"onUpdate:modelValue":t[2]||(t[2]=e=>x(E)?E.value=e:null),placeholder:m(K),clearable:""},null,8,["modelValue","placeholder"]),c(o,{type:"primary",onClick:t[3]||(t[3]=e=>J(1))},{default:f((()=>t[8]||(t[8]=[v("查 询")]))),_:1})]),p("div",U,[p("div",H,[p("span",{class:u(["mr-2",m(F)?"text-[#409eff]":""])},"翻译",2),c(n,{modelValue:m(F),"onUpdate:modelValue":t[4]||(t[4]=e=>x(F)?F.value=e:null)},null,8,["modelValue"])])]),p("div",null,[(r(!0),d(g,null,h(m(I).content,((e,l)=>(r(),d("div",{class:"flex mb-8",key:l},[p("div",L,y(l+1)+".",1),p("div",null,[p("div",{class:"text-[18px] text-[#5e5e5e] cursor-pointer html-value",innerHTML:e.text,onClick:a=>((e,l)=>{let a=`${e}.${l}`.replace(/\n/g,"");W.value+=`<p>${a}</p>`})(l+1,e.text)},null,8,M),m(F)?(r(),d("div",{key:0,class:"text-[16px] text-[#0a76f5] mt-4",innerHTML:e.translateText},null,8,q)):b("",!0)])])))),128))]),m(I)&&m(I).eleTotal?(r(),j(i,{key:0,class:"pb-10",total:m(I).eleTotal,page:m(A).pageNo,"onUpdate:page":t[5]||(t[5]=e=>m(A).pageNo=e),limit:m(A).pageSize,"onUpdate:limit":t[6]||(t[6]=e=>m(A).pageSize=e),onPagination:J},null,8,["total","page","limit"])):b("",!0)]),p("div",$,[(r(),j(w,{class:"h-[380px] fixed z-99",style:k({width:m(D)+"px"}),modelValue:m(W),"onUpdate:modelValue":t[7]||(t[7]=e=>x(W)?W.value=e:null),onClear:O,key:m(B)},null,8,["style","modelValue"]))])])}}},[["__scopeId","data-v-27bf6704"]]);export{E as default};
