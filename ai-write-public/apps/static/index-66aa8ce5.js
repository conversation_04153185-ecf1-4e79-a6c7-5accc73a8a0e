/* empty css                  *//* empty css                *//* empty css                 */import{s as e}from"./index-a38e48f6.js";import{r as a,aL as l,d as s,e as t,f as u,g as i,q as r,j as n,m as o,k as v,F as m,z as p,T as d,h as c,aM as f,i as x,a2 as y,C as j,E as h,H as g}from"https://static.medsci.cn/ai-write/static/index-62ea6ab9.js";import{s as k}from"./index-428d0c42.js";/* empty css                   */const w={class:"pt-10 overflow-auto h-full"},F={class:"flex justify-center my-10"},_={key:0},b=["innerHTML"],z={class:"flex justify-center mt-10"},C={__name:"index",setup(C){const E=a(""),H=a([]),L=a([]),M=a(5),S=()=>{if(a=E.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(a)||!E.value)return y.warning("请输入英文内容");var a;e({text:E.value}).then((e=>{e&&e&&e.data&&(H.value=e.data,H.value=k(H.value),M.value=5,T())}))},T=()=>{let e=[];5==M.value?(e=[0,5],M.value=3):3==M.value?(e=[5,8],M.value=2):2==M.value&&(e=[8,10],M.value=5),L.value=JSON.parse(JSON.stringify(H.value)).slice(e[0],e[1])};return(e,a)=>{const y=j,k=h,C=g,H=l("copy");return s(),t("div",w,[u(y,{modelValue:i(E),"onUpdate:modelValue":a[0]||(a[0]=e=>r(E)?E.value=e:null),rows:8,type:"textarea",placeholder:"请输入不超过250单词的段落","show-word-limit":"",resize:"none",maxlength:250},null,8,["modelValue"]),n("div",F,[u(k,{type:"primary",onClick:S},{default:o((()=>[v("改 写")])),_:1})]),i(L)&&i(L).length?(s(),t("div",_,[(s(!0),t(m,null,p(i(L),((e,a)=>(s(),t("div",{class:"flex justify-center mb-6 relative",key:a},[n("span",{innerHTML:e.textShow,class:"text-[18px]"},null,8,b),d((s(),c(C,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-2 absolute"},{default:o((()=>[u(i(f))])),_:2},1024)),[[H,e.text]])])))),128)),n("div",z,[u(k,{type:"primary",link:"",onClick:T},{default:o((()=>[v("换一换")])),_:1})])])):x("",!0)])}}};export{C as default};
