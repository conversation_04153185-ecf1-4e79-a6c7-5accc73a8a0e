import{_ as e,a as l}from"./Editor-21e01250.js";/* empty css                  *//* empty css                 */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                    *//* empty css                  */import{a}from"./index-d432b816.js";import{s as t}from"./index-9feca5bf.js";/* empty css                   */import{_ as s}from"./_plugin-vue_export-helper-1b428a4d.js";import{r as o,N as r,o as n,d as i,e as u,j as d,F as p,y as m,R as c,g as v,i as f,t as b,f as x,q as g,m as y,k as h,h as k,a3 as V,B as j,E as _,ae as w,aH as C,aI as S,aJ as T}from"https://static.medsci.cn/ai-write/static/index-fe292e2d.js";/* empty css                  */const M={class:"h-full flex overflow-auto"},N={class:"flex flex-col w-[160px] mr-4"},U=["onClick"],z={key:0,class:"w-[4px] h-full"},H={class:"flex-1"},$={class:"flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2"},L={class:"flex items-center my-8 overflow-hidden"},A={class:"flex items-center mr-4"},I={class:"flex items-center mr-2"},R={class:"flex items-center"},q={class:"relative flex-1"},E={class:"text-[#419eff] absolute font-bold -top-1.5 left-[30%]"},Y={class:"mr-2"},B=["innerHTML","onClick"],D=["innerHTML"],F=s({__name:"index",setup(s){const F=o("类风湿关节炎铁死亡特征基因图的构建"),G=o(""),J=o(1),K=o(!0),P=o([]),W=o([1990,2023]),O=o({}),Q=r({pageNo:1,pageSize:20}),X=o([{label:"Title",value:"title"},{label:"Keywords",value:"keyword"},{label:"Abstract",value:"abstract"},{label:"Introduction",value:"introduction"},{label:"Methods",value:"methods"},{label:"Results",value:"results"},{label:"Discussion",value:"discussion"},{label:"Acknowledge",value:"acknowleg"},{label:"全库检索(CNS)",value:"all"}]),Z=o("title"),ee=e=>{if(!F.value)return V.warning("请输入关键词或短句");1==e&&(Q.pageNo=1);let l=P.value.map((e=>({field:"effect",opt:2,vals:[e],val:"",synonymsWordVos:[]})));a(Z.value,{key:F.value,page:Q.pageNo-1,size:Q.pageSize,allMySentence:0,allMyGroupSentence:0,synonymsHistory:0,mulSearchConditions:l,beginYear:W.value[0],endYear:W.value[1],mySentenceRange:0,sorts:[]}).then((e=>{e&&e.data&&(O.value=e.data,O.value.content=t(O.value.content))}))},le=()=>{J.value++},ae=()=>{ee()};return n((()=>{ee()})),(a,t)=>{const s=j,o=_,r=e,n=w,V=C,te=S,se=T,oe=l;return i(),u("div",M,[d("div",N,[(i(!0),u(p,null,m(v(X),((e,l)=>(i(),u("div",{class:c(["nav-item flex items-center font-bold text-[#2d3858] cursor-pointer border border-solid border-top-0 border-left-0 border-right-0 border-gray-200 h-[46px]",v(Z)==e.value?"bg-[#7e90b8]":"bg-[#f8f8f8]"]),key:l,onClick:l=>(e=>{Z.value=e.value,F.value="",O.value={},P.value=[]})(e)},[v(Z)!=e.value?(i(),u("div",z)):f("",!0),d("div",{class:c(["pl-10 h-full flex items-center",v(Z)==e.value?"border border-solid border-left-4 border-[#2b3858] border-top-0 border-bottom-0 border-right-0":""])},b(e.label),3)],10,U)))),128))]),d("div",H,[d("div",$,[x(s,{class:"h-full !text-[24px]",modelValue:v(F),"onUpdate:modelValue":t[0]||(t[0]=e=>g(F)?F.value=e:null),placeholder:"这里输入关键词或短句，中英文均可",clearable:""},null,8,["modelValue"]),x(o,{type:"primary",onClick:t[1]||(t[1]=e=>ee(1))},{default:y((()=>t[8]||(t[8]=[h("查 询")]))),_:1})]),(i(),k(r,{class:"h-[380px] mb-4",modelValue:v(G),"onUpdate:modelValue":t[2]||(t[2]=e=>g(G)?G.value=e:null),onClear:le,key:v(J)},null,8,["modelValue"])),d("div",L,[d("div",A,[d("div",{class:c(["mr-2",v(K)?"text-[#409eff]":""])},"翻译",2),x(n,{modelValue:v(K),"onUpdate:modelValue":t[3]||(t[3]=e=>g(K)?K.value=e:null)},null,8,["modelValue"])]),d("div",I,[t[12]||(t[12]=d("span",{class:"mr-2"},"影响因子：",-1)),x(te,{modelValue:v(P),"onUpdate:modelValue":t[4]||(t[4]=e=>g(P)?P.value=e:null)},{default:y((()=>[x(V,{label:"3"},{default:y((()=>t[9]||(t[9]=[h(b("<3分"))]))),_:1}),x(V,{label:"5"},{default:y((()=>t[10]||(t[10]=[h("3-10分")]))),_:1}),x(V,{label:"10"},{default:y((()=>t[11]||(t[11]=[h(b(">10分"))]))),_:1})])),_:1},8,["modelValue"])]),d("div",R,[t[13]||(t[13]=d("div",{class:"w-[60px]"},"年份范围",-1)),d("div",q,[x(se,{class:"ml-6 !w-[132px]",modelValue:v(W),"onUpdate:modelValue":t[5]||(t[5]=e=>g(W)?W.value=e:null),range:"",max:2023,min:1990,onChange:ae},null,8,["modelValue"]),d("span",E,b(`${v(W)[0]}-${v(W)[1]}`),1)])])]),d("div",null,[(i(!0),u(p,null,m(v(O).content,((e,l)=>(i(),u("div",{class:"flex mb-8",key:l},[d("div",Y,b(l+1)+".",1),d("div",null,[d("div",{class:"text-[18px] text-[#5e5e5e] cursor-pointer html-value",innerHTML:e.text,onClick:a=>((e,l)=>{let a=`${e}.${l}`.replace(/\n/g,"");G.value+=`<p>${a}</p>`})(l+1,e.text)},null,8,B),v(K)?(i(),u("div",{key:0,class:"text-[16px] text-[#0a76f5] mt-4",innerHTML:e.translateText},null,8,D)):f("",!0)])])))),128))]),v(O)&&v(O).eleTotal?(i(),k(oe,{key:0,class:"pb-10",total:v(O).eleTotal,page:v(Q).pageNo,"onUpdate:page":t[6]||(t[6]=e=>v(Q).pageNo=e),limit:v(Q).pageSize,"onUpdate:limit":t[7]||(t[7]=e=>v(Q).pageSize=e),onPagination:ee},null,8,["total","page","limit"])):f("",!0)])])}}},[["__scopeId","data-v-5eb438e1"]]);export{F as default};
