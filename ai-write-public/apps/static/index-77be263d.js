/* empty css                  */import{_ as e,c as t}from"./index-9adde0d7.js";import{N as i,r as a,P as s,f as o,w as r,V as n,a as l,d as c,e as d,i as u,j as p,t as h,a9 as m,aa as g,b as f,W as v,M as y,an as w,o as b,g as k,s as x,F as C,z as _,a1 as I,T,U as S,k as A,m as P,a5 as B,Y as D,aj as M,ao as E,E as L,ap as N,aq as O,y as U,ar as R,c as $,h as F,u as j,a6 as z,as as H,at as G,v as q,l as V,$ as J,q as W,ah as Q,au as K,Z as X,av as Y,H as Z,C as ee,aw as te,ax as ie,ay as ae,x as se}from"./index-22ad0034.js";/* empty css                 *//* empty css                *//* empty css                   */import{a as oe,M as re,n as ne,e as le,D as ce,t as de,E as ue,L as pe,x as he,U as me,N as ge,p as fe}from"./use-touch-02334b45.js";import{I as ve,T as ye,P as we}from"./index-53fbb62f.js";import{g as be}from"./index-a80adfe9.js";const[ke,xe]=oe("checkbox-group"),Ce=Symbol(ke),_e={name:re,disabled:Boolean,iconSize:ne,modelValue:re,checkedColor:String,labelPosition:String,labelDisabled:Boolean};var Ie=i({props:le({},_e,{bem:ce(Function),role:String,shape:String,parent:Object,checked:Boolean,bindGroup:de,indeterminate:{type:Boolean,default:null}}),emits:["click","toggle"],setup(e,{emit:t,slots:i}){const r=a(),n=t=>{if(e.parent&&e.bindGroup)return e.parent.props[t]},l=s((()=>{if(e.parent&&e.bindGroup){const t=n("disabled")||e.disabled;if("checkbox"===e.role){const i=n("modelValue").length,a=n("max");return t||a&&i>=+a&&!e.checked}return t}return e.disabled})),c=s((()=>n("direction"))),d=s((()=>{const t=e.checkedColor||n("checkedColor");if(t&&e.checked&&!l.value)return{borderColor:t,backgroundColor:t}})),u=s((()=>e.shape||n("shape")||"round")),p=i=>{const{target:a}=i,s=r.value,o=s===a||(null==s?void 0:s.contains(a));l.value||!o&&e.labelDisabled||t("toggle"),t("click",i)},h=()=>{var t,a;const{bem:s,checked:c,indeterminate:p}=e,h=e.iconSize||n("iconSize");return o("div",{ref:r,class:s("icon",[u.value,{disabled:l.value,checked:c,indeterminate:p}]),style:"dot"!==u.value?{fontSize:ue(h)}:{width:ue(h),height:ue(h),borderColor:null==(t=d.value)?void 0:t.borderColor}},[i.icon?i.icon({checked:c,disabled:l.value}):"dot"!==u.value?o(ve,{name:p?"minus":"success",style:d.value},null):o("div",{class:s("icon--dot__icon"),style:{backgroundColor:null==(a=d.value)?void 0:a.backgroundColor}},null)])},m=()=>{const{checked:t}=e;if(i.default)return o("span",{class:e.bem("label",[e.labelPosition,{disabled:l.value}])},[i.default({checked:t,disabled:l.value})])};return()=>{const t="left"===e.labelPosition?[m(),h()]:[h(),m()];return o("div",{role:e.role,class:e.bem([{disabled:l.value,"label-disabled":e.labelDisabled},c.value]),tabindex:l.value?void 0:0,"aria-checked":e.checked,onClick:p},[t])}}});const[Te,Se]=oe("checkbox");const Ae=fe(i({name:Te,props:le({},_e,{shape:String,bindGroup:de,indeterminate:{type:Boolean,default:null}}),emits:["change","update:modelValue"],setup(e,{emit:t,slots:i}){const{parent:a}=pe(Ce),l=s((()=>a&&e.bindGroup?-1!==a.props.modelValue.indexOf(e.name):!!e.modelValue)),c=(i=!l.value)=>{a&&e.bindGroup?(t=>{const{name:i}=e,{max:s,modelValue:o}=a.props,r=o.slice();if(t)s&&r.length>=+s||r.includes(i)||(r.push(i),e.bindGroup&&a.updateValue(r));else{const t=r.indexOf(i);-1!==t&&(r.splice(t,1),e.bindGroup&&a.updateValue(r))}})(i):t("update:modelValue",i),null!==e.indeterminate&&t("change",i)};return r((()=>e.modelValue),(i=>{null===e.indeterminate&&t("change",i)})),he({toggle:c,props:e,checked:l}),me((()=>e.modelValue)),()=>o(Ie,n({bem:Se,role:"checkbox",parent:a,checked:l.value,onToggle:c},e),ge(i,["default","icon"]))}})),Pe={name:"FooterNav",data:()=>({showFooter:!1}),head(){},computed:{},mounted(){jQuery(document).ready((function(){jQuery("#footOwl").owlCarousel({items:3,margin:17,responsiveClass:!0,dots:!0,loop:!0,autoplay:!0,autoplayTimeout:1e4,autoplayHoverPause:!0,responsive:{}})}))},methods:{userFeedBack(){window.open("https://www.medsci.cn/message/list.do","_blank")}}},Be=e=>(m("data-v-9c198f31"),e=e(),g(),e),De={class:"bg-[#F7F7F7] bg"},Me={key:0,id:"footer"},Ee=[Be((()=>p("div",{class:"wrapper mobile-b w-footer-wrapper",id:"foot"},[p("div",{class:"footer-widgets w-footer-widgets lets-do-4"},[p("div",{class:"widget-split item phone-hidden"},[p("div",{class:"widget ms-footer-img"},[p("div",null,[p("p",null,[p("a",{href:"https://www.medsci.cn",class:"ms-statis","ms-statis":"link"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/416a6450-b970-11ec-a1b8-6123b3ff61ea_MSLOGO1.png",alt:""})])]),p("p",{class:"bold w-footer-bold"},"梅斯医学MedSci-临床医生发展平台"),p("p",{class:"text-justify w-footer-des"}," 梅斯医学是面向医生的综合互联网平台，应用大数据和人工智能技术链接医生、患者、药械企业等，提供精准数字化医学传播解决方案，优化医疗生态，改善医疗质量，共创美好生活。 ")])])]),p("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[p("div",{class:"widget"},[p("h3",{class:"w-footer-h3"},"关于我们"),p("div",{class:"clearfix"},[p("ul",{class:"menu left"},[p("li",{class:"ms-link iconfont"},[p("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=9",class:"ms-statis","ms-statis":"link"},"关于我们")]),p("li",{class:"ms-link iconfont"},[p("a",{target:"_blank",href:"https://www.medsci.cn/recruit/job-list",class:"ms-statis","ms-statis":"link"},"加入我们")]),p("li",{class:"ms-link iconfont"},[p("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=21",class:"ms-statis","ms-statis":"link"},"版权合作")]),p("li",{class:"ms-link iconfont"},[p("a",{target:"_blank",href:"https://ir.medsci.cn/zh_cn/",class:"ms-statis","ms-statis":"link"},"投资者关系")]),p("li",{class:"ms-link iconfont"},[p("a",{target:"_blank",href:"http://medscihealthcare.com/",class:"ms-statis","ms-statis":"link"},"MedSci Healthcare")]),p("li",{class:"ms-link iconfont"},[p("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=19",class:"ms-statis","ms-statis":"link"},"友情链接")])])])])]),p("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[p("div",{class:"widget"},[p("h3",{class:"w-footer-h3"},"我们的业务"),p("div",{class:"clearfix"},[p("ul",{class:"menu left"},[p("li",{class:"ms-link iconfont"},[p("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"真实世界研究")]),p("li",{class:"ms-link iconfont"},[p("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"科研数智化")]),p("li",{class:"ms-link iconfont"},[p("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"数字化学术传播")])])])])]),p("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[p("div",{class:"widget"},[p("h3",{class:"w-footer-h3"},"我们的产品"),p("div",{class:"clearfix"},[p("ul",{class:"menu left"},[p("li",{class:"ms-link iconfont"},[p("a",{href:"https://www.medsci.cn/sci/index.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"期刊智能查询")]),p("li",{class:"ms-link iconfont"},[p("a",{href:"https://www.medsci.cn/sci/nsfc.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"国自然查询分析")]),p("li",{class:"ms-link iconfont"},[p("a",{href:"https://www.medsci.cn/guideline/search",target:"_blank",class:"ms-statis","ms-statis":"link"},"临床指南")]),p("li",{class:"ms-link iconfont"},[p("a",{href:"https://m.medsci.cn/scale",target:"_blank",class:"ms-statis","ms-statis":"link"},"医学公式计算")]),p("li",{class:"ms-link iconfont"},[p("a",{href:"https://dict.bioon.com/",target:"_blank",class:"ms-statis","ms-statis":"link"},"医药生物大词典")]),p("li",{class:"ms-link iconfont"},[p("a",{href:"https://class.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯精品课")]),p("li",{class:"ms-link iconfont"},[p("a",{href:"https://open.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯公开课")])])])])]),p("div",{class:"w-footer-right phone-hidden"},[p("div",{class:"widget"},[p("h3",{class:"w-footer-h3"},"新媒体矩阵"),p("div",{id:"footOwl",class:"owl-carousel"},[p("div",{class:"item w-owl-item"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_gzh.png",alt:""}),p("span",null,"梅斯医学")]),p("div",{class:"item w-owl-item"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_zhongliu.png",alt:""}),p("span",null,"肿瘤新前沿")]),p("div",{class:"item w-owl-item"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_xueye.jpg",alt:""}),p("span",null,"血液新前沿")]),p("div",{class:"item w-owl-item"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_medsci_fengshi.jpg",alt:""}),p("span",null,"风湿新前沿")]),p("div",{class:"item w-owl-item"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/caaaa270-93a5-11ec-bca5-7f892b5df5d6_medsci_xqy.png",alt:""}),p("span",null,"呼吸新前沿")]),p("div",{class:"item w-owl-item"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_pifu.jpg",alt:""}),p("span",null,"皮肤新前沿")]),p("div",{class:"item w-owl-item"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/bce723f0-609d-11ed-b66b-937b834e3ef9_sjqianyan.jpg",alt:""}),p("span",null,"神经新前沿")]),p("div",{class:"item w-owl-item"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xiaohua.jpg",alt:""}),p("span",null,"消化新前沿")]),p("div",{class:"item w-owl-item"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xinxueguan.jpg",alt:""}),p("span",null,"心血管新前沿")]),p("div",{class:"item w-owl-item"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/dc22e340-957c-11ec-bca5-7f892b5df5d6_ms_shengwugu.png",alt:""}),p("span",null,"生物谷")]),p("div",{class:"item w-owl-item"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_app.png",alt:""}),p("span",null,"MedSci App")])])])])])],-1)))],Le={class:"footer-copyright ms-footer-copy w-footer-copy"},Ne={href:"https://www.medsci.cn/about/index.do?id=18",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},Oe=Be((()=>p("span",{style:{margin:"0px 20px"}},"|",-1))),Ue={href:"https://www.medsci.cn/about/index.do?id=9",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"};const Re=l(Pe,[["render",function(e,t,i,a,s,o){return c(),d("footer",De,[s.showFooter?(c(),d("div",Me,Ee)):u("",!0),p("div",Le,[p("p",null,[p("a",Ne,h(e.$t("market.privacyPolicy")),1),Oe,p("a",Ue,h(e.$t("market.termService")),1)])])])}],["__scopeId","data-v-9c198f31"]]);function $e(e){return""===e?e:"true"===e||"1"==e}function Fe(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function je(e,t){for(var i,a="",s=0,o=-1,r=0,n=0;n<=e.length;++n){if(n<e.length)i=e.charCodeAt(n);else{if(47===i)break;i=47}if(47===i){if(o===n-1||1===r);else if(o!==n-1&&2===r){if(a.length<2||2!==s||46!==a.charCodeAt(a.length-1)||46!==a.charCodeAt(a.length-2))if(a.length>2){var l=a.lastIndexOf("/");if(l!==a.length-1){-1===l?(a="",s=0):s=(a=a.slice(0,l)).length-1-a.lastIndexOf("/"),o=n,r=0;continue}}else if(2===a.length||1===a.length){a="",s=0,o=n,r=0;continue}t&&(a.length>0?a+="/..":a="..",s=2)}else a.length>0?a+="/"+e.slice(o+1,n):a=e.slice(o+1,n),s=n-o-1;o=n,r=0}else 46===i&&-1!==r?++r:r=-1}return a}var ze={resolve:function(){for(var e,t="",i=!1,a=arguments.length-1;a>=-1&&!i;a--){var s;a>=0?s=arguments[a]:(void 0===e&&(e=process.cwd()),s=e),Fe(s),0!==s.length&&(t=s+"/"+t,i=47===s.charCodeAt(0))}return t=je(t,!i),i?t.length>0?"/"+t:"/":t.length>0?t:"."},normalize:function(e){if(Fe(e),0===e.length)return".";var t=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=je(e,!t)).length||t||(e="."),e.length>0&&i&&(e+="/"),t?"/"+e:e},isAbsolute:function(e){return Fe(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,t=0;t<arguments.length;++t){var i=arguments[t];Fe(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":ze.normalize(e)},relative:function(e,t){if(Fe(e),Fe(t),e===t)return"";if((e=ze.resolve(e))===(t=ze.resolve(t)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var a=e.length,s=a-i,o=1;o<t.length&&47===t.charCodeAt(o);++o);for(var r=t.length-o,n=s<r?s:r,l=-1,c=0;c<=n;++c){if(c===n){if(r>n){if(47===t.charCodeAt(o+c))return t.slice(o+c+1);if(0===c)return t.slice(o+c)}else s>n&&(47===e.charCodeAt(i+c)?l=c:0===c&&(l=0));break}var d=e.charCodeAt(i+c);if(d!==t.charCodeAt(o+c))break;47===d&&(l=c)}var u="";for(c=i+l+1;c<=a;++c)c!==a&&47!==e.charCodeAt(c)||(0===u.length?u+="..":u+="/..");return u.length>0?u+t.slice(o+l):(o+=l,47===t.charCodeAt(o)&&++o,t.slice(o))},_makeLong:function(e){return e},dirname:function(e){if(Fe(e),0===e.length)return".";for(var t=e.charCodeAt(0),i=47===t,a=-1,s=!0,o=e.length-1;o>=1;--o)if(47===(t=e.charCodeAt(o))){if(!s){a=o;break}}else s=!1;return-1===a?i?"/":".":i&&1===a?"//":e.slice(0,a)},basename:function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');Fe(e);var i,a=0,s=-1,o=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t.length===e.length&&t===e)return"";var r=t.length-1,n=-1;for(i=e.length-1;i>=0;--i){var l=e.charCodeAt(i);if(47===l){if(!o){a=i+1;break}}else-1===n&&(o=!1,n=i+1),r>=0&&(l===t.charCodeAt(r)?-1==--r&&(s=i):(r=-1,s=n))}return a===s?s=n:-1===s&&(s=e.length),e.slice(a,s)}for(i=e.length-1;i>=0;--i)if(47===e.charCodeAt(i)){if(!o){a=i+1;break}}else-1===s&&(o=!1,s=i+1);return-1===s?"":e.slice(a,s)},extname:function(e){Fe(e);for(var t=-1,i=0,a=-1,s=!0,o=0,r=e.length-1;r>=0;--r){var n=e.charCodeAt(r);if(47!==n)-1===a&&(s=!1,a=r+1),46===n?-1===t?t=r:1!==o&&(o=1):-1!==t&&(o=-1);else if(!s){i=r+1;break}}return-1===t||-1===a||0===o||1===o&&t===a-1&&t===i+1?"":e.slice(t,a)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var i=t.dir||t.root,a=t.base||(t.name||"")+(t.ext||"");return i?i===t.root?i+a:i+e+a:a}("/",e)},parse:function(e){Fe(e);var t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;var i,a=e.charCodeAt(0),s=47===a;s?(t.root="/",i=1):i=0;for(var o=-1,r=0,n=-1,l=!0,c=e.length-1,d=0;c>=i;--c)if(47!==(a=e.charCodeAt(c)))-1===n&&(l=!1,n=c+1),46===a?-1===o?o=c:1!==d&&(d=1):-1!==o&&(d=-1);else if(!l){r=c+1;break}return-1===o||-1===n||0===d||1===d&&o===n-1&&o===r+1?-1!==n&&(t.base=t.name=0===r&&s?e.slice(1,n):e.slice(r,n)):(0===r&&s?(t.name=e.slice(1,o),t.base=e.slice(1,n)):(t.name=e.slice(r,o),t.base=e.slice(r,n)),t.ext=e.slice(o,n)),r>0?t.dir=e.slice(0,r-1):s&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};ze.posix=ze;const He=ze.extname,Ge=ze.basename;class qe{constructor(){let e="undefined"==typeof global,t="image/png",i="image/jpeg",a="image/jpeg",s="image/webp",o="application/pdf",r="image/svg+xml";Object.assign(this,{toMime:this.toMime.bind(this),fromMime:this.fromMime.bind(this),expected:e?'"png", "jpg", or "webp"':'"png", "jpg", "pdf", or "svg"',formats:e?{png:t,jpg:i,jpeg:a,webp:s}:{png:t,jpg:i,jpeg:a,pdf:o,svg:r},mimes:e?{[t]:"png",[i]:"jpg",[s]:"webp"}:{[t]:"png",[i]:"jpg",[o]:"pdf",[r]:"svg"}})}toMime(e){return this.formats[(e||"").replace(/^\./,"").toLowerCase()]}fromMime(e){return this.mimes[e]}}class Ve{static for(e){return(new Ve).append(e).get()}constructor(){this.crc=-1}get(){return~this.crc}append(e){for(var t=0|this.crc,i=this.table,a=0,s=0|e.length;a<s;a++)t=t>>>8^i[255&(t^e[a])];return this.crc=t,this}}function Je(e){let t=new Uint8Array(e),i=new DataView(t.buffer),a={array:t,view:i,size:e,set8:(e,t)=>(i.setUint8(e,t),a),set16:(e,t)=>(i.setUint16(e,t,!0),a),set32:(e,t)=>(i.setUint32(e,t,!0),a),bytes:(e,i)=>(t.set(i,e),a)};return a}Ve.prototype.table=(()=>{var e,t,i,a=[];for(e=0;e<256;e++){for(i=e,t=0;t<8;t++)i=1&i?i>>>1^3988292384:i>>>1;a[e]=i}return a})();class We{constructor(e){let t=new Date;Object.assign(this,{directory:e,offset:0,files:[],time:(t.getHours()<<6|t.getMinutes())<<5|t.getSeconds()/2,date:(t.getFullYear()-1980<<4|t.getMonth()+1)<<5|t.getDate()}),this.add(e)}async add(e,t){let i=!t,a=We.encoder.encode(`${this.directory}/${i?"":e}`),s=new Uint8Array(i?0:await t.arrayBuffer()),o=30+a.length,r=o+s.length,{offset:n}=this,l=Je(26).set32(0,134742036).set16(6,this.time).set16(8,this.date).set32(10,Ve.for(s)).set32(14,s.length).set32(18,s.length).set16(22,a.length);n+=o;let c=Je(o+s.length+16).set32(0,67324752).bytes(4,l.array).bytes(30,a).bytes(o,s);n+=s.length,c.set32(r,134695760).bytes(r+4,l.array.slice(10,22)),n+=16,this.files.push({offset:n,folder:i,name:a,header:l,payload:c}),this.offset=n}toBuffer(){let e=this.files.reduce(((e,{name:t})=>46+t.length+e),0),t=Je(e+22),i=0;for(var{offset:a,name:s,header:o,folder:r}of this.files)t.set32(i,33639248).set16(i+4,20).bytes(i+6,o.array).set8(i+38,r?16:0).set32(i+42,a).bytes(i+46,s),i+=46+s.length;t.set32(i,101010256).set16(i+8,this.files.length).set16(i+10,this.files.length).set32(i+12,e).set32(i+16,this.offset);let n=new Uint8Array(this.offset+t.size),l=0;for(var{payload:c}of this.files)n.set(c.array,l),l+=c.size;return n.set(t.array,l),n}get blob(){return new Blob([this.toBuffer()],{type:"application/zip"})}}We.encoder=new TextEncoder;const Qe=(e,t,i,a)=>{if(a){let{width:t,height:i}=e,s=Object.assign(document.createElement("canvas"),{width:t,height:i}),o=s.getContext("2d");o.fillStyle=a,o.fillRect(0,0,t,i),o.drawImage(e,0,0),e=s}return new Promise(((a,s)=>e.toBlob(a,t,i)))},Ke=(e,t)=>{const i=window.URL.createObjectURL(t),a=document.createElement("a");a.style.display="none",a.href=i,a.setAttribute("download",e),void 0===a.download&&a.setAttribute("target","_blank"),document.body.appendChild(a),a.click(),document.body.removeChild(a),setTimeout((()=>window.URL.revokeObjectURL(i)),100)},Xe={asBuffer:(...e)=>Qe(...e).then((e=>e.arrayBuffer())),asDownload:async(e,t,i,a,s)=>{Ke(s,await Qe(e,t,i,a))},asZipDownload:async(e,t,i,a,s,o,r)=>{let n=Ge(s,".zip")||"archive",l=new We(n);await Promise.all(e.map((async(e,s)=>{let n=(e=>o.replace("{}",String(e+1).padStart(r,"0")))(s);await l.add(n,await Qe(e,t,i,a))}))),Ke(`${n}.zip`,l.blob)},atScale:(e,t,i)=>e.map((e=>{if(1==t&&!i)return e.canvas;let a=document.createElement("canvas"),s=a.getContext("2d"),o=e.canvas?e.canvas:e;return a.width=o.width*t,a.height=o.height*t,i&&(s.fillStyle=i,s.fillRect(0,0,a.width,a.height)),s.scale(t,t),s.drawImage(o,0,0),a})),options:function(e,{filename:t="",extension:i="",format:a,page:s,quality:o,matte:r,density:n,outline:l,archive:c}={}){var{fromMime:d,toMime:u,expected:p}=new qe,h=(c=c||"canvas",a||i.replace(/@\d+x$/i,"")||He(t)),m=(a=d(u(h)||h),u(a)),g=e.length;if(!h)throw new Error("Cannot determine image format (use a filename extension or 'format' argument)");if(!a)throw new Error(`Unsupported file format "${h}" (expected ${p})`);if(!g)throw new RangeError("Canvas has no associated contexts (try calling getContext or newPage first)");let f,v,y=t.replace(/{(\d*)}/g,((e,t)=>(v=!0,t=parseInt(t,10),f=isFinite(t)?t:isFinite(f)?f:-1,"{}"))),w=s>0?s-1:s<0?g+s:void 0;if(isFinite(w)&&w<0||w>=g)throw new RangeError(1==g?`Canvas only has a ‘page 1’ (${w} is out of bounds)`:`Canvas has pages 1–${g} (${w} is out of bounds)`);if(e=isFinite(w)?[e[w]]:v||"pdf"==a?e:e.slice(-1),void 0===o)o=.92;else if("number"!=typeof o||!isFinite(o)||o<0||o>1)throw new TypeError("The quality option must be an number in the 0.0–1.0 range");if(void 0===n){let e=(i||Ge(t,h)).match(/@(\d+)x$/i);n=e?parseInt(e[1],10):1}else if("number"!=typeof n||!Number.isInteger(n)||n<1)throw new TypeError("The density option must be a non-negative integer");return void 0===l?l=!0:"svg"==a&&(l=!!l),{filename:t,pattern:y,format:a,mime:m,pages:e,padding:f,quality:o,matte:r,density:n,outline:l,archive:c}}},{asBuffer:Ye,asDownload:Ze,asZipDownload:et,atScale:tt,options:it}=Xe,at=Symbol.for("toDataURL");const{CanvasRenderingContext2D:st,CanvasGradient:ot,CanvasPattern:rt,Image:nt,ImageData:lt,Path2D:ct,DOMMatrix:dt,DOMRect:ut,DOMPoint:pt}=window,ht={Canvas:class{constructor(e,t){let i=document.createElement("canvas"),a=[];for(var[s,o]of(Object.defineProperty(i,"async",{value:!0,writable:!1,enumerable:!0}),Object.entries({png:()=>Ye(i,"image/png"),jpg:()=>Ye(i,"image/jpeg"),pages:()=>a.concat(i).map((e=>e.getContext("2d")))})))Object.defineProperty(i,s,{get:o});return Object.assign(i,{width:e,height:t,newPage(...e){var{width:t,height:s}=i,o=Object.assign(document.createElement("canvas"),{width:t,height:s});o.getContext("2d").drawImage(i,0,0),a.push(o);var[t,s]=e.length?e:[t,s];return Object.assign(i,{width:t,height:s}).getContext("2d")},saveAs(e,t){t="number"==typeof t?{quality:t}:t;let i=it(this.pages,{filename:e,...t}),{pattern:a,padding:s,mime:o,quality:r,matte:n,density:l,archive:c}=i,d=tt(i.pages,l);return null==s?Ze(d[0],o,r,n,e):et(d,o,r,n,c,a,s)},toBuffer(e="png",t={}){t="number"==typeof t?{quality:t}:t;let i=it(this.pages,{extension:e,...t}),{mime:a,quality:s,matte:o,pages:r,density:n}=i,l=tt(r,n,o)[0];return Ye(l,a,s,o)},[at]:i.toDataURL.bind(i),toDataURL(e="png",t={}){t="number"==typeof t?{quality:t}:t;let a=it(this.pages,{extension:e,...t}),{mime:s,quality:o,matte:r,pages:n,density:l}=a,c=tt(n,l,r)[0],d=c[c===i?at:"toDataURL"](s,o);return Promise.resolve(d)}})}},loadImage:e=>new Promise(((t,i)=>Object.assign(new nt,{crossOrigin:"Anonymous",onload:t,onerror:i,src:e}))),CanvasRenderingContext2D:st,CanvasGradient:ot,CanvasPattern:rt,Image:nt,ImageData:lt,Path2D:ct,DOMMatrix:dt,DOMRect:ut,DOMPoint:pt},mt=(e,t,i={},a=i)=>{if(Array.isArray(t))t.forEach((t=>mt(e,t,i,a)));else if("function"==typeof t)t(e,i,a,mt);else{const s=Object.keys(t)[0];Array.isArray(t[s])?(a[s]={},mt(e,t[s],i,a[s])):a[s]=t[s](e,i,a,mt)}return i},gt=(e,t)=>(i,a,s,o)=>{t(i,a,s)&&o(i,e,a,s)},ft=(e=0)=>t=>t.data[t.pos+e],vt=e=>t=>t.data.subarray(t.pos,t.pos+=e),yt=e=>t=>t.data.subarray(t.pos,t.pos+e),wt=e=>t=>Array.from(vt(e)(t)).map((e=>String.fromCharCode(e))).join(""),bt=e=>t=>{const i=vt(2)(t);return e?(i[1]<<8)+i[0]:(i[0]<<8)+i[1]},kt=(e,t)=>(i,a,s)=>{const o="function"==typeof t?t(i,a,s):t,r=vt(e),n=new Array(o);for(var l=0;l<o;l++)n[l]=r(i);return n},xt=e=>t=>{const i=(e=>e.data[e.pos++])(t),a=new Array(8);for(var s=0;s<8;s++)a[7-s]=!!(i&1<<s);return Object.keys(e).reduce(((t,i)=>{const s=e[i];return s.length?t[i]=((e,t,i)=>{for(var a=0,s=0;s<i;s++)a+=e[t+s]&&2**(i-s-1);return a})(a,s.index,s.length):t[i]=a[s.index],t}),{})};var Ct={blocks:e=>{const t=[],i=e.data.length;for(var a=0,s=(e=>e.data[e.pos++])(e);0!==s&&s;s=(e=>e.data[e.pos++])(e)){if(e.pos+s>=i){const s=i-e.pos;t.push(vt(s)(e)),a+=s;break}t.push(vt(s)(e)),a+=s}const o=new Uint8Array(a);for(var r=0,n=0;n<t.length;n++)o.set(t[n],r),r+=t[n].length;return o}};const _t=gt({gce:[{codes:vt(2)},{byteSize:e=>e.data[e.pos++]},{extras:xt({future:{index:0,length:3},disposal:{index:3,length:3},userInput:{index:6},transparentColorGiven:{index:7}})},{delay:bt(!0)},{transparentColorIndex:e=>e.data[e.pos++]},{terminator:e=>e.data[e.pos++]}]},(e=>{var t=yt(2)(e);return 33===t[0]&&249===t[1]})),It=gt({image:[{code:e=>e.data[e.pos++]},{descriptor:[{left:bt(!0)},{top:bt(!0)},{width:bt(!0)},{height:bt(!0)},{lct:xt({exists:{index:0},interlaced:{index:1},sort:{index:2},future:{index:3,length:2},size:{index:5,length:3}})}]},gt({lct:kt(3,((e,t,i)=>Math.pow(2,i.descriptor.lct.size+1)))},((e,t,i)=>i.descriptor.lct.exists)),{data:[{minCodeSize:e=>e.data[e.pos++]},Ct]}]},(e=>44===ft()(e))),Tt=gt({text:[{codes:vt(2)},{blockSize:e=>e.data[e.pos++]},{preData:(e,t,i)=>vt(i.text.blockSize)(e)},Ct]},(e=>{var t=yt(2)(e);return 33===t[0]&&1===t[1]})),St=gt({application:[{codes:vt(2)},{blockSize:e=>e.data[e.pos++]},{id:(e,t,i)=>wt(i.blockSize)(e)},Ct]},(e=>{var t=yt(2)(e);return 33===t[0]&&255===t[1]})),At=gt({comment:[{codes:vt(2)},Ct]},(e=>{var t=yt(2)(e);return 33===t[0]&&254===t[1]})),Pt=[{header:[{signature:wt(3)},{version:wt(3)}]},{lsd:[{width:bt(!0)},{height:bt(!0)},{gct:xt({exists:{index:0},resolution:{index:1,length:3},sort:{index:4},size:{index:5,length:3}})},{backgroundColorIndex:e=>e.data[e.pos++]},{pixelAspectRatio:e=>e.data[e.pos++]}]},gt({gct:kt(3,((e,t)=>Math.pow(2,t.lsd.gct.size+1)))},((e,t)=>t.lsd.gct.exists)),{frames:(Bt=[_t,St,At,It,Tt],Dt=e=>{var t=ft()(e);return 33===t||44===t},(e,t,i,a)=>{const s=[];let o=e.pos;for(;Dt(e,t,i);){const i={};if(a(e,Bt,t,i),e.pos===o)break;o=e.pos,s.push(i)}return s})}];var Bt,Dt;const Mt=(e,t,i)=>{if(!e.image)return;const{image:a}=e,s=a.descriptor.width*a.descriptor.height;var o=((e,t,i)=>{const a=4096,s=i;var o,r,n,l,c,d,u,p,h,m;const g=new Array(i),f=new Array(a),v=new Array(a),y=new Array(4097);for(c=1+(r=1<<(m=e)),o=r+2,u=-1,n=(1<<(l=m+1))-1,p=0;p<r;p++)f[p]=0,v[p]=p;var w,b,k,x,C,_;for(w=b=k=x=C=_=0,h=0;h<s;){if(0===x){if(b<l){w+=t[_]<<b,b+=8,_++;continue}if(p=w&n,w>>=l,b-=l,p>o||p==c)break;if(p==r){n=(1<<(l=m+1))-1,o=r+2,u=-1;continue}if(-1==u){y[x++]=v[p],u=p,k=p;continue}for(d=p,p==o&&(y[x++]=k,p=u);p>r;)y[x++]=v[p],p=f[p];k=255&v[p],y[x++]=k,o<a&&(f[o]=u,v[o]=k,0==(++o&n)&&o<a&&(l++,n+=o)),u=d}x--,g[C++]=y[x],h++}for(h=C;h<s;h++)g[h]=0;return g})(a.data.minCodeSize,a.data.blocks,s);a.descriptor.lct.interlaced&&(o=((e,t)=>{const i=new Array(e.length),a=e.length/t,s=function(a,s){const o=e.slice(s*t,(s+1)*t);i.splice.apply(i,[a*t,t].concat(o))},o=[0,4,2,1],r=[8,8,4,2];for(var n=0,l=0;l<4;l++)for(var c=o[l];c<a;c+=r[l])s(c,n),n++;return i})(o,a.descriptor.width));const r={pixels:o,dims:{top:e.image.descriptor.top,left:e.image.descriptor.left,width:e.image.descriptor.width,height:e.image.descriptor.height}};return a.descriptor.lct&&a.descriptor.lct.exists?r.colorTable=a.lct:r.colorTable=t,e.gce&&(r.delay=10*(e.gce.delay||10),r.disposalType=e.gce.extras.disposal,e.gce.extras.transparentColorGiven&&(r.transparentIndex=e.gce.transparentColorIndex)),i&&(r.patch=(e=>{const t=e.pixels.length,i=new Uint8ClampedArray(4*t);for(var a=0;a<t;a++){const t=4*a,s=e.pixels[a],o=e.colorTable[s];i[t]=o[0],i[t+1]=o[1],i[t+2]=o[2],i[t+3]=s!==e.transparentIndex?255:0}return i})(r)),r};function Et(e){var t=encodeURI(e).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return t.length+(t.length!=Number(e)?3:0)}class Lt{constructor(e){this.mode=Ut.MODE_8BIT_BYTE,this.parsedData=[],this.data=e;const t=[];for(let i=0,a=this.data.length;i<a;i++){const e=[],a=this.data.charCodeAt(i);a>65536?(e[0]=240|(1835008&a)>>>18,e[1]=128|(258048&a)>>>12,e[2]=128|(4032&a)>>>6,e[3]=128|63&a):a>2048?(e[0]=224|(61440&a)>>>12,e[1]=128|(4032&a)>>>6,e[2]=128|63&a):a>128?(e[0]=192|(1984&a)>>>6,e[1]=128|63&a):e[0]=a,t.push(e)}this.parsedData=Array.prototype.concat.apply([],t),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}getLength(){return this.parsedData.length}write(e){for(let t=0,i=this.parsedData.length;t<i;t++)e.put(this.parsedData[t],8)}}class Nt{constructor(e=-1,t=Ot.L){this.moduleCount=0,this.dataList=[],this.typeNumber=e,this.errorCorrectLevel=t,this.moduleCount=0,this.dataList=[]}addData(e){if(this.typeNumber<=0)this.typeNumber=function(e,t){for(var i=1,a=Et(e),s=0,o=Gt.length;s<o;s++){var r=0;switch(t){case Ot.L:r=Gt[s][0];break;case Ot.M:r=Gt[s][1];break;case Ot.Q:r=Gt[s][2];break;case Ot.H:r=Gt[s][3]}if(a<=r)break;i++}if(i>Gt.length)throw new Error("Too long data");return i}(e,this.errorCorrectLevel);else{if(this.typeNumber>40)throw new Error(`Invalid QR version: ${this.typeNumber}`);if(!function(e,t,i){const a=Et(t),s=e-1;let o=0;switch(i){case Ot.L:o=Gt[s][0];break;case Ot.M:o=Gt[s][1];break;case Ot.Q:o=Gt[s][2];break;case Ot.H:o=Gt[s][3]}return a<=o}(this.typeNumber,e,this.errorCorrectLevel))throw new Error(`Data is too long for QR version: ${this.typeNumber}`)}const t=new Lt(e);this.dataList.push(t),this.dataCache=void 0}isDark(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(`${e},${t}`);return this.modules[e][t]}getModuleCount(){return this.moduleCount}make(){this.makeImpl(!1,this.getBestMaskPattern())}makeImpl(e,t){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(let i=0;i<this.moduleCount;i++){this.modules[i]=new Array(this.moduleCount);for(let e=0;e<this.moduleCount;e++)this.modules[i][e]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=Nt.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)}setupPositionProbePattern(e,t){for(let i=-1;i<=7;i++)if(!(e+i<=-1||this.moduleCount<=e+i))for(let a=-1;a<=7;a++)t+a<=-1||this.moduleCount<=t+a||(this.modules[e+i][t+a]=0<=i&&i<=6&&(0==a||6==a)||0<=a&&a<=6&&(0==i||6==i)||2<=i&&i<=4&&2<=a&&a<=4)}getBestMaskPattern(){if(Number.isInteger(this.maskPattern)&&Object.values(Rt).includes(this.maskPattern))return this.maskPattern;let e=0,t=0;for(let i=0;i<8;i++){this.makeImpl(!0,i);const a=$t.getLostPoint(this);(0==i||e>a)&&(e=a,t=i)}return t}setupTimingPattern(){for(let e=8;e<this.moduleCount-8;e++)null==this.modules[e][6]&&(this.modules[e][6]=e%2==0);for(let e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)}setupPositionAdjustPattern(){const e=$t.getPatternPosition(this.typeNumber);for(let t=0;t<e.length;t++)for(let i=0;i<e.length;i++){const a=e[t],s=e[i];if(null==this.modules[a][s])for(let e=-2;e<=2;e++)for(let t=-2;t<=2;t++)this.modules[a+e][s+t]=-2==e||2==e||-2==t||2==t||0==e&&0==t}}setupTypeNumber(e){const t=$t.getBCHTypeNumber(this.typeNumber);for(var i=0;i<18;i++){var a=!e&&1==(t>>i&1);this.modules[Math.floor(i/3)][i%3+this.moduleCount-8-3]=a}for(i=0;i<18;i++){a=!e&&1==(t>>i&1);this.modules[i%3+this.moduleCount-8-3][Math.floor(i/3)]=a}}setupTypeInfo(e,t){const i=this.errorCorrectLevel<<3|t,a=$t.getBCHTypeInfo(i);for(var s=0;s<15;s++){var o=!e&&1==(a>>s&1);s<6?this.modules[s][8]=o:s<8?this.modules[s+1][8]=o:this.modules[this.moduleCount-15+s][8]=o}for(s=0;s<15;s++){o=!e&&1==(a>>s&1);s<8?this.modules[8][this.moduleCount-s-1]=o:s<9?this.modules[8][15-s-1+1]=o:this.modules[8][15-s-1]=o}this.modules[this.moduleCount-8][8]=!e}mapData(e,t){let i=-1,a=this.moduleCount-1,s=7,o=0;for(let r=this.moduleCount-1;r>0;r-=2)for(6==r&&r--;;){for(let i=0;i<2;i++)if(null==this.modules[a][r-i]){let n=!1;o<e.length&&(n=1==(e[o]>>>s&1));$t.getMask(t,a,r-i)&&(n=!n),this.modules[a][r-i]=n,s--,-1==s&&(o++,s=7)}if(a+=i,a<0||this.moduleCount<=a){a-=i,i=-i;break}}}static createData(e,t,i){const a=zt.getRSBlocks(e,t),s=new Ht;for(var o=0;o<i.length;o++){const t=i[o];s.put(t.mode,4),s.put(t.getLength(),$t.getLengthInBits(t.mode,e)),t.write(s)}let r=0;for(o=0;o<a.length;o++)r+=a[o].dataCount;if(s.getLengthInBits()>8*r)throw new Error(`code length overflow. (${s.getLengthInBits()}>${8*r})`);for(s.getLengthInBits()+4<=8*r&&s.put(0,4);s.getLengthInBits()%8!=0;)s.putBit(!1);for(;!(s.getLengthInBits()>=8*r||(s.put(Nt.PAD0,8),s.getLengthInBits()>=8*r));)s.put(Nt.PAD1,8);return Nt.createBytes(s,a)}static createBytes(e,t){let i=0,a=0,s=0;const o=new Array(t.length),r=new Array(t.length);for(var n=0;n<t.length;n++){const c=t[n].dataCount,d=t[n].totalCount-c;a=Math.max(a,c),s=Math.max(s,d),o[n]=new Array(c);for(var l=0;l<o[n].length;l++)o[n][l]=255&e.buffer[l+i];i+=c;const u=$t.getErrorCorrectPolynomial(d),p=new jt(o[n],u.getLength()-1).mod(u);r[n]=new Array(u.getLength()-1);for(l=0;l<r[n].length;l++){const e=l+p.getLength()-r[n].length;r[n][l]=e>=0?p.get(e):0}}let c=0;for(l=0;l<t.length;l++)c+=t[l].totalCount;const d=new Array(c);let u=0;for(l=0;l<a;l++)for(n=0;n<t.length;n++)l<o[n].length&&(d[u++]=o[n][l]);for(l=0;l<s;l++)for(n=0;n<t.length;n++)l<r[n].length&&(d[u++]=r[n][l]);return d}}Nt.PAD0=236,Nt.PAD1=17;const Ot={L:1,M:0,Q:3,H:2},Ut={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},Rt={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};class $t{static getBCHTypeInfo(e){let t=e<<10;for(;$t.getBCHDigit(t)-$t.getBCHDigit($t.G15)>=0;)t^=$t.G15<<$t.getBCHDigit(t)-$t.getBCHDigit($t.G15);return(e<<10|t)^$t.G15_MASK}static getBCHTypeNumber(e){let t=e<<12;for(;$t.getBCHDigit(t)-$t.getBCHDigit($t.G18)>=0;)t^=$t.G18<<$t.getBCHDigit(t)-$t.getBCHDigit($t.G18);return e<<12|t}static getBCHDigit(e){let t=0;for(;0!=e;)t++,e>>>=1;return t}static getPatternPosition(e){return $t.PATTERN_POSITION_TABLE[e-1]}static getMask(e,t,i){switch(e){case Rt.PATTERN000:return(t+i)%2==0;case Rt.PATTERN001:return t%2==0;case Rt.PATTERN010:return i%3==0;case Rt.PATTERN011:return(t+i)%3==0;case Rt.PATTERN100:return(Math.floor(t/2)+Math.floor(i/3))%2==0;case Rt.PATTERN101:return t*i%2+t*i%3==0;case Rt.PATTERN110:return(t*i%2+t*i%3)%2==0;case Rt.PATTERN111:return(t*i%3+(t+i)%2)%2==0;default:throw new Error(`bad maskPattern:${e}`)}}static getErrorCorrectPolynomial(e){let t=new jt([1],0);for(let i=0;i<e;i++)t=t.multiply(new jt([1,Ft.gexp(i)],0));return t}static getLengthInBits(e,t){if(1<=t&&t<10)switch(e){case Ut.MODE_NUMBER:return 10;case Ut.MODE_ALPHA_NUM:return 9;case Ut.MODE_8BIT_BYTE:case Ut.MODE_KANJI:return 8;default:throw new Error(`mode:${e}`)}else if(t<27)switch(e){case Ut.MODE_NUMBER:return 12;case Ut.MODE_ALPHA_NUM:return 11;case Ut.MODE_8BIT_BYTE:return 16;case Ut.MODE_KANJI:return 10;default:throw new Error(`mode:${e}`)}else{if(!(t<41))throw new Error(`type:${t}`);switch(e){case Ut.MODE_NUMBER:return 14;case Ut.MODE_ALPHA_NUM:return 13;case Ut.MODE_8BIT_BYTE:return 16;case Ut.MODE_KANJI:return 12;default:throw new Error(`mode:${e}`)}}}static getLostPoint(e){const t=e.getModuleCount();let i=0;for(var a=0;a<t;a++)for(var s=0;s<t;s++){let o=0;const r=e.isDark(a,s);for(let i=-1;i<=1;i++)if(!(a+i<0||t<=a+i))for(let n=-1;n<=1;n++)s+n<0||t<=s+n||0==i&&0==n||r==e.isDark(a+i,s+n)&&o++;o>5&&(i+=3+o-5)}for(a=0;a<t-1;a++)for(s=0;s<t-1;s++){let t=0;e.isDark(a,s)&&t++,e.isDark(a+1,s)&&t++,e.isDark(a,s+1)&&t++,e.isDark(a+1,s+1)&&t++,0!=t&&4!=t||(i+=3)}for(a=0;a<t;a++)for(s=0;s<t-6;s++)e.isDark(a,s)&&!e.isDark(a,s+1)&&e.isDark(a,s+2)&&e.isDark(a,s+3)&&e.isDark(a,s+4)&&!e.isDark(a,s+5)&&e.isDark(a,s+6)&&(i+=40);for(s=0;s<t;s++)for(a=0;a<t-6;a++)e.isDark(a,s)&&!e.isDark(a+1,s)&&e.isDark(a+2,s)&&e.isDark(a+3,s)&&e.isDark(a+4,s)&&!e.isDark(a+5,s)&&e.isDark(a+6,s)&&(i+=40);let o=0;for(s=0;s<t;s++)for(a=0;a<t;a++)e.isDark(a,s)&&o++;return i+=10*(Math.abs(100*o/t/t-50)/5),i}}$t.PATTERN_POSITION_TABLE=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],$t.G15=1335,$t.G18=7973,$t.G15_MASK=21522;class Ft{static glog(e){if(e<1)throw new Error(`glog(${e})`);return Ft.LOG_TABLE[e]}static gexp(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return Ft.EXP_TABLE[e]}}Ft.EXP_TABLE=new Array(256),Ft.LOG_TABLE=new Array(256),Ft._constructor=function(){for(var e=0;e<8;e++)Ft.EXP_TABLE[e]=1<<e;for(e=8;e<256;e++)Ft.EXP_TABLE[e]=Ft.EXP_TABLE[e-4]^Ft.EXP_TABLE[e-5]^Ft.EXP_TABLE[e-6]^Ft.EXP_TABLE[e-8];for(e=0;e<255;e++)Ft.LOG_TABLE[Ft.EXP_TABLE[e]]=e}();class jt{constructor(e,t){if(null==e.length)throw new Error(`${e.length}/${t}`);let i=0;for(;i<e.length&&0==e[i];)i++;this.num=new Array(e.length-i+t);for(let a=0;a<e.length-i;a++)this.num[a]=e[a+i]}get(e){return this.num[e]}getLength(){return this.num.length}multiply(e){const t=new Array(this.getLength()+e.getLength()-1);for(let i=0;i<this.getLength();i++)for(let a=0;a<e.getLength();a++)t[i+a]^=Ft.gexp(Ft.glog(this.get(i))+Ft.glog(e.get(a)));return new jt(t,0)}mod(e){if(this.getLength()-e.getLength()<0)return this;const t=Ft.glog(this.get(0))-Ft.glog(e.get(0)),i=new Array(this.getLength());for(var a=0;a<this.getLength();a++)i[a]=this.get(a);for(a=0;a<e.getLength();a++)i[a]^=Ft.gexp(Ft.glog(e.get(a))+t);return new jt(i,0).mod(e)}}class zt{constructor(e,t){this.totalCount=e,this.dataCount=t}static getRSBlocks(e,t){const i=zt.getRsBlockTable(e,t);if(null==i)throw new Error(`bad rs block @ typeNumber:${e}/errorCorrectLevel:${t}`);const a=i.length/3,s=[];for(let o=0;o<a;o++){const e=i[3*o+0],t=i[3*o+1],a=i[3*o+2];for(let i=0;i<e;i++)s.push(new zt(t,a))}return s}static getRsBlockTable(e,t){switch(t){case Ot.L:return zt.RS_BLOCK_TABLE[4*(e-1)+0];case Ot.M:return zt.RS_BLOCK_TABLE[4*(e-1)+1];case Ot.Q:return zt.RS_BLOCK_TABLE[4*(e-1)+2];case Ot.H:return zt.RS_BLOCK_TABLE[4*(e-1)+3];default:return}}}zt.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];class Ht{constructor(){this.buffer=[],this.length=0}get(e){const t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)}put(e,t){for(let i=0;i<t;i++)this.putBit(1==(e>>>t-i-1&1))}getLengthInBits(){return this.length}putBit(e){const t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}}const Gt=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];var qt=256,Vt=1024,Jt=1<<18;function Wt(e,t){var i,a,s,o,r;function n(e,t,a,s,o){i[t][0]-=e*(i[t][0]-a)/Vt,i[t][1]-=e*(i[t][1]-s)/Vt,i[t][2]-=e*(i[t][2]-o)/Vt}function l(e,t,a,s,o){for(var n,l,c=Math.abs(t-e),d=Math.min(t+e,qt),u=t+1,p=t-1,h=1;u<d||p>c;)l=r[h++],u<d&&((n=i[u++])[0]-=l*(n[0]-a)/Jt,n[1]-=l*(n[1]-s)/Jt,n[2]-=l*(n[2]-o)/Jt),p>c&&((n=i[p--])[0]-=l*(n[0]-a)/Jt,n[1]-=l*(n[1]-s)/Jt,n[2]-=l*(n[2]-o)/Jt)}function c(e,t,a){var r,n,l,c,d,u=~(1<<31),p=u,h=-1,m=h;for(r=0;r<qt;r++)n=i[r],(l=Math.abs(n[0]-e)+Math.abs(n[1]-t)+Math.abs(n[2]-a))<u&&(u=l,h=r),(c=l-(s[r]>>12))<p&&(p=c,m=r),d=o[r]>>10,o[r]-=d,s[r]+=d<<10;return o[h]+=64,s[h]-=65536,m}this.buildColormap=function(){!function(){var e,t;for(i=[],a=new Int32Array(256),s=new Int32Array(qt),o=new Int32Array(qt),r=new Int32Array(32),e=0;e<qt;e++)t=(e<<12)/qt,i[e]=new Float64Array([t,t,t,0]),o[e]=256,s[e]=0}(),function(){var i,a,s,o,d,u,p=e.length,h=30+(t-1)/3,m=p/(3*t),g=~~(m/100),f=Vt,v=2048,y=v>>6;for(y<=1&&(y=0),i=0;i<y;i++)r[i]=f*(256*(y*y-i*i)/(y*y));p<1509?(t=1,a=3):a=p%499!=0?1497:p%491!=0?1473:p%487!=0?1461:1509;var w=0;for(i=0;i<m;)if(n(f,u=c(s=(255&e[w])<<4,o=(255&e[w+1])<<4,d=(255&e[w+2])<<4),s,o,d),0!==y&&l(y,u,s,o,d),(w+=a)>=p&&(w-=p),0===g&&(g=1),++i%g==0)for(f-=f/h,(y=(v-=v/30)>>6)<=1&&(y=0),u=0;u<y;u++)r[u]=f*(256*(y*y-u*u)/(y*y))}(),function(){for(var e=0;e<qt;e++)i[e][0]>>=4,i[e][1]>>=4,i[e][2]>>=4,i[e][3]=e}(),function(){var e,t,s,o,r,n,l=0,c=0;for(e=0;e<qt;e++){for(r=e,n=(s=i[e])[1],t=e+1;t<qt;t++)(o=i[t])[1]<n&&(r=t,n=o[1]);if(o=i[r],e!=r&&(t=o[0],o[0]=s[0],s[0]=t,t=o[1],o[1]=s[1],s[1]=t,t=o[2],o[2]=s[2],s[2]=t,t=o[3],o[3]=s[3],s[3]=t),n!=l){for(a[l]=c+e>>1,t=l+1;t<n;t++)a[t]=e;l=n,c=e}}for(a[l]=c+255>>1,t=l+1;t<256;t++)a[t]=255}()},this.getColormap=function(){for(var e=[],t=[],a=0;a<qt;a++)t[i[a][3]]=a;for(var s=0,o=0;o<qt;o++){var r=t[o];e[s++]=i[r][0],e[s++]=i[r][1],e[s++]=i[r][2]}return e},this.lookupRGB=function(e,t,s){for(var o,r,n,l=1e3,c=-1,d=a[t],u=d-1;d<qt||u>=0;)d<qt&&((n=(r=i[d])[1]-t)>=l?d=qt:(d++,n<0&&(n=-n),(o=r[0]-e)<0&&(o=-o),(n+=o)<l&&((o=r[2]-s)<0&&(o=-o),(n+=o)<l&&(l=n,c=r[3])))),u>=0&&((n=t-(r=i[u])[1])>=l?u=-1:(u--,n<0&&(n=-n),(o=r[0]-e)<0&&(o=-o),(n+=o)<l&&((o=r[2]-s)<0&&(o=-o),(n+=o)<l&&(l=n,c=r[3]))));return c}}var Qt=5003,Kt=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535];function Xt(e,t,i,a){var s,o,r,n,l,c,d,u,p,h=Math.max(2,a),m=new Uint8Array(256),g=new Int32Array(Qt),f=new Int32Array(Qt),v=0,y=0,w=!1;function b(e,t){m[o++]=e,o>=254&&C(t)}function k(e){x(Qt),y=l+2,w=!0,T(l,e)}function x(e){for(var t=0;t<e;++t)g[t]=-1}function C(e){o>0&&(e.writeByte(o),e.writeBytes(m,0,o),o=0)}function _(e){return(1<<e)-1}function I(){return 0===d?-1:(--d,255&i[u++])}function T(e,t){for(s&=Kt[v],v>0?s|=e<<v:s=e,v+=p;v>=8;)b(255&s,t),s>>=8,v-=8;if((y>r||w)&&(w?(r=_(p=n),w=!1):(++p,r=12==p?4096:_(p))),e==c){for(;v>0;)b(255&s,t),s>>=8,v-=8;C(t)}}this.encode=function(i){i.writeByte(h),d=e*t,u=0,function(e,t){var i,a,s,d,u,h,m;for(w=!1,r=_(p=n=e),c=1+(l=1<<e-1),y=l+2,o=0,d=I(),m=0,i=Qt;i<65536;i*=2)++m;m=8-m,x(h=Qt),T(l,t);e:for(;-1!=(a=I());)if(i=(a<<12)+d,g[s=a<<m^d]!==i){if(g[s]>=0){u=h-s,0===s&&(u=1);do{if((s-=u)<0&&(s+=h),g[s]===i){d=f[s];continue e}}while(g[s]>=0)}T(d,t),d=a,y<4096?(f[s]=y++,g[s]=i):k(t)}else d=f[s];T(d,t),T(c,t)}(h+1,i),i.writeByte(0)}}function Yt(){this.page=-1,this.pages=[],this.newPage()}Yt.pageSize=4096,Yt.charMap={};for(var Zt=0;Zt<256;Zt++)Yt.charMap[Zt]=String.fromCharCode(Zt);function ei(e,t){this.width=~~e,this.height=~~t,this.transparent=null,this.transIndex=0,this.repeat=-1,this.delay=0,this.image=null,this.pixels=null,this.indexedPixels=null,this.colorDepth=null,this.colorTab=null,this.neuQuant=null,this.usedEntry=new Array,this.palSize=7,this.dispose=-1,this.firstFrame=!0,this.sample=10,this.dither=!1,this.globalPalette=!1,this.out=new Yt}Yt.prototype.newPage=function(){this.pages[++this.page]=new Uint8Array(Yt.pageSize),this.cursor=0},Yt.prototype.getData=function(){for(var e="",t=0;t<this.pages.length;t++)for(var i=0;i<Yt.pageSize;i++)e+=Yt.charMap[this.pages[t][i]];return e},Yt.prototype.toFlattenUint8Array=function(){const e=[];for(var t=0;t<this.pages.length;t++)if(t===this.pages.length-1){const i=Uint8Array.from(this.pages[t].slice(0,this.cursor));e.push(i)}else e.push(this.pages[t]);const i=new Uint8Array(e.reduce(((e,t)=>e+t.length),0));return e.reduce(((e,t)=>(i.set(t,e),e+t.length)),0),i},Yt.prototype.writeByte=function(e){this.cursor>=Yt.pageSize&&this.newPage(),this.pages[this.page][this.cursor++]=e},Yt.prototype.writeUTFBytes=function(e){for(var t=e.length,i=0;i<t;i++)this.writeByte(e.charCodeAt(i))},Yt.prototype.writeBytes=function(e,t,i){for(var a=i||e.length,s=t||0;s<a;s++)this.writeByte(e[s])},ei.prototype.setDelay=function(e){this.delay=Math.round(e/10)},ei.prototype.setFrameRate=function(e){this.delay=Math.round(100/e)},ei.prototype.setDispose=function(e){e>=0&&(this.dispose=e)},ei.prototype.setRepeat=function(e){this.repeat=e},ei.prototype.setTransparent=function(e){this.transparent=e},ei.prototype.addFrame=function(e){this.image=e,this.colorTab=this.globalPalette&&this.globalPalette.slice?this.globalPalette:null,this.getImagePixels(),this.analyzePixels(),!0===this.globalPalette&&(this.globalPalette=this.colorTab),this.firstFrame&&(this.writeHeader(),this.writeLSD(),this.writePalette(),this.repeat>=0&&this.writeNetscapeExt()),this.writeGraphicCtrlExt(),this.writeImageDesc(),this.firstFrame||this.globalPalette||this.writePalette(),this.writePixels(),this.firstFrame=!1},ei.prototype.finish=function(){this.out.writeByte(59)},ei.prototype.setQuality=function(e){e<1&&(e=1),this.sample=e},ei.prototype.setDither=function(e){!0===e&&(e="FloydSteinberg"),this.dither=e},ei.prototype.setGlobalPalette=function(e){this.globalPalette=e},ei.prototype.getGlobalPalette=function(){return this.globalPalette&&this.globalPalette.slice&&this.globalPalette.slice(0)||this.globalPalette},ei.prototype.writeHeader=function(){this.out.writeUTFBytes("GIF89a")},ei.prototype.analyzePixels=function(){this.colorTab||(this.neuQuant=new Wt(this.pixels,this.sample),this.neuQuant.buildColormap(),this.colorTab=this.neuQuant.getColormap()),this.dither?this.ditherPixels(this.dither.replace("-serpentine",""),null!==this.dither.match(/-serpentine/)):this.indexPixels(),this.pixels=null,this.colorDepth=8,this.palSize=7,null!==this.transparent&&(this.transIndex=this.findClosest(this.transparent,!0))},ei.prototype.indexPixels=function(e){var t=this.pixels.length/3;this.indexedPixels=new Uint8Array(t);for(var i=0,a=0;a<t;a++){var s=this.findClosestRGB(255&this.pixels[i++],255&this.pixels[i++],255&this.pixels[i++]);this.usedEntry[s]=!0,this.indexedPixels[a]=s}},ei.prototype.ditherPixels=function(e,t){var i={FalseFloydSteinberg:[[3/8,1,0],[3/8,0,1],[2/8,1,1]],FloydSteinberg:[[7/16,1,0],[3/16,-1,1],[5/16,0,1],[1/16,1,1]],Stucki:[[8/42,1,0],[4/42,2,0],[2/42,-2,1],[4/42,-1,1],[8/42,0,1],[4/42,1,1],[2/42,2,1],[1/42,-2,2],[2/42,-1,2],[4/42,0,2],[2/42,1,2],[1/42,2,2]],Atkinson:[[1/8,1,0],[1/8,2,0],[1/8,-1,1],[1/8,0,1],[1/8,1,1],[1/8,0,2]]};if(!e||!i[e])throw"Unknown dithering kernel: "+e;var a=i[e],s=0,o=this.height,r=this.width,n=this.pixels,l=t?-1:1;this.indexedPixels=new Uint8Array(this.pixels.length/3);for(var c=0;c<o;c++){t&&(l*=-1);for(var d=1==l?0:r-1,u=1==l?r:0;d!==u;d+=l){var p=3*(s=c*r+d),h=n[p],m=n[p+1],g=n[p+2];p=this.findClosestRGB(h,m,g),this.usedEntry[p]=!0,this.indexedPixels[s]=p,p*=3;for(var f=h-this.colorTab[p],v=m-this.colorTab[p+1],y=g-this.colorTab[p+2],w=1==l?0:a.length-1,b=1==l?a.length:0;w!==b;w+=l){var k=a[w][1],x=a[w][2];if(k+d>=0&&k+d<r&&x+c>=0&&x+c<o){var C=a[w][0];p=s+k+x*r,n[p*=3]=Math.max(0,Math.min(255,n[p]+f*C)),n[p+1]=Math.max(0,Math.min(255,n[p+1]+v*C)),n[p+2]=Math.max(0,Math.min(255,n[p+2]+y*C))}}}}},ei.prototype.findClosest=function(e,t){return this.findClosestRGB((16711680&e)>>16,(65280&e)>>8,255&e,t)},ei.prototype.findClosestRGB=function(e,t,i,a){if(null===this.colorTab)return-1;if(this.neuQuant&&!a)return this.neuQuant.lookupRGB(e,t,i);for(var s=0,o=16777216,r=this.colorTab.length,n=0,l=0;n<r;l++){var c=e-(255&this.colorTab[n++]),d=t-(255&this.colorTab[n++]),u=i-(255&this.colorTab[n++]),p=c*c+d*d+u*u;(!a||this.usedEntry[l])&&p<o&&(o=p,s=l)}return s},ei.prototype.getImagePixels=function(){var e=this.width,t=this.height;this.pixels=new Uint8Array(e*t*3);for(var i=this.image,a=0,s=0,o=0;o<t;o++)for(var r=0;r<e;r++)this.pixels[s++]=i[a++],this.pixels[s++]=i[a++],this.pixels[s++]=i[a++],a++},ei.prototype.writeGraphicCtrlExt=function(){var e,t;this.out.writeByte(33),this.out.writeByte(249),this.out.writeByte(4),null===this.transparent?(e=0,t=0):(e=1,t=2),this.dispose>=0&&(t=7&this.dispose),t<<=2,this.out.writeByte(0|t|e),this.writeShort(this.delay),this.out.writeByte(this.transIndex),this.out.writeByte(0)},ei.prototype.writeImageDesc=function(){this.out.writeByte(44),this.writeShort(0),this.writeShort(0),this.writeShort(this.width),this.writeShort(this.height),this.firstFrame||this.globalPalette?this.out.writeByte(0):this.out.writeByte(128|this.palSize)},ei.prototype.writeLSD=function(){this.writeShort(this.width),this.writeShort(this.height),this.out.writeByte(240|this.palSize),this.out.writeByte(0),this.out.writeByte(0)},ei.prototype.writeNetscapeExt=function(){this.out.writeByte(33),this.out.writeByte(255),this.out.writeByte(11),this.out.writeUTFBytes("NETSCAPE2.0"),this.out.writeByte(3),this.out.writeByte(1),this.writeShort(this.repeat),this.out.writeByte(0)},ei.prototype.writePalette=function(){this.out.writeBytes(this.colorTab);for(var e=768-this.colorTab.length,t=0;t<e;t++)this.out.writeByte(0)},ei.prototype.writeShort=function(e){this.out.writeByte(255&e),this.out.writeByte(e>>8&255)},ei.prototype.writePixels=function(){new Xt(this.width,this.height,this.indexedPixels,this.colorDepth).encode(this.out)},ei.prototype.stream=function(){return this.out};var ti=globalThis&&globalThis.__awaiter||function(e,t,i,a){return new(i||(i=Promise))((function(s,o){function r(e){try{l(a.next(e))}catch(t){o(t)}}function n(e){try{l(a.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?s(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(r,n)}l((a=a.apply(e,t||[])).next())}))};const{Canvas:ii}=ht,ai=.4;function si(e){if(e)return new Promise((function(i,a){if("data"==e.slice(0,4)){let s=new Image;return s.onload=function(){i(s),t(s)},s.onerror=function(){a("Image load error"),t(s)},void(s.src=e)}let s=new Image;s.setAttribute("crossOrigin","Anonymous"),s.onload=function(){i(s)},s.onerror=function(){a("Image load error")},s.src=e}));function t(e){e.onload=null,e.onerror=null}}class oi{constructor(e){const t=Object.assign({},e);if(Object.keys(oi.defaultOptions).forEach((e=>{e in t||Object.defineProperty(t,e,{value:oi.defaultOptions[e],enumerable:!0,writable:!0})})),t.components?"object"==typeof t.components&&Object.keys(oi.defaultComponentOptions).forEach((e=>{e in t.components?Object.defineProperty(t.components,e,{value:Object.assign(Object.assign({},oi.defaultComponentOptions[e]),t.components[e]),enumerable:!0,writable:!0}):Object.defineProperty(t.components,e,{value:oi.defaultComponentOptions[e],enumerable:!0,writable:!0})})):t.components=oi.defaultComponentOptions,null!==t.dotScale&&void 0!==t.dotScale){if(t.dotScale<=0||t.dotScale>1)throw new Error("dotScale should be in range (0, 1].");t.components.data.scale=t.dotScale,t.components.timing.scale=t.dotScale,t.components.alignment.scale=t.dotScale}this.options=t,this.canvas=new ii(e.size,e.size),this.canvasContext=this.canvas.getContext("2d"),this.qrCode=new Nt(-1,this.options.correctLevel),Number.isInteger(this.options.maskPattern)&&(this.qrCode.maskPattern=this.options.maskPattern),Number.isInteger(this.options.version)&&(this.qrCode.typeNumber=this.options.version),this.qrCode.addData(this.options.text),this.qrCode.make()}draw(){return new Promise((e=>this._draw().then(e)))}_clear(){this.canvasContext.clearRect(0,0,this.canvas.width,this.canvas.height)}static _prepareRoundedCornerClip(e,t,i,a,s,o){e.beginPath(),e.moveTo(t,i),e.arcTo(t+a,i,t+a,i+s,o),e.arcTo(t+a,i+s,t,i+s,o),e.arcTo(t,i+s,t,i,o),e.arcTo(t,i,t+a,i,o),e.closePath()}static _getAverageRGB(e){const t={r:0,g:0,b:0};let i,a,s=-4;const o={r:0,g:0,b:0};let r=0;a=e.naturalHeight||e.height,i=e.naturalWidth||e.width;const n=new ii(i,a).getContext("2d");if(!n)return t;let l;n.drawImage(e,0,0);try{l=n.getImageData(0,0,i,a)}catch(c){return t}for(;(s+=20)<l.data.length;)l.data[s]>200||l.data[s+1]>200||l.data[s+2]>200||(++r,o.r+=l.data[s],o.g+=l.data[s+1],o.b+=l.data[s+2]);return o.r=~~(o.r/r),o.g=~~(o.g/r),o.b=~~(o.b/r),o}static _drawDot(e,t,i,a,s=0,o=1){e.fillRect((t+s)*a,(i+s)*a,o*a,o*a)}static _drawAlignProtector(e,t,i,a){e.clearRect((t-2)*a,(i-2)*a,5*a,5*a),e.fillRect((t-2)*a,(i-2)*a,5*a,5*a)}static _drawAlign(e,t,i,a,s=0,o=1,r,n){const l=e.fillStyle;e.fillStyle=r,new Array(4).fill(0).map(((r,n)=>{oi._drawDot(e,t-2+n,i-2,a,s,o),oi._drawDot(e,t+2,i-2+n,a,s,o),oi._drawDot(e,t+2-n,i+2,a,s,o),oi._drawDot(e,t-2,i+2-n,a,s,o)})),oi._drawDot(e,t,i,a,s,o),n||(e.fillStyle="rgba(255, 255, 255, 0.6)",new Array(2).fill(0).map(((r,n)=>{oi._drawDot(e,t-1+n,i-1,a,s,o),oi._drawDot(e,t+1,i-1+n,a,s,o),oi._drawDot(e,t+1-n,i+1,a,s,o),oi._drawDot(e,t-1,i+1-n,a,s,o)}))),e.fillStyle=l}_draw(){var e,t,i,a,s,o,r,n,l,c,d,u,p,h,m,g,f,v,y;return ti(this,void 0,void 0,(function*(){const w=null===(e=this.qrCode)||void 0===e?void 0:e.moduleCount,b=this.options.size;let k=this.options.margin;(k<0||2*k>=b)&&(k=0);const x=Math.ceil(k),C=b-2*k,_=this.options.whiteMargin,I=this.options.backgroundDimming,T=Math.ceil(C/w),S=T*w,A=S+2*x,P=new ii(A,A),B=P.getContext("2d");this._clear(),B.save(),B.translate(x,x);const D=new ii(A,A),M=D.getContext("2d");let E=null,L=[];if(this.options.gifBackground){const e=(e=>{const t=new Uint8Array(e);return mt({data:t,pos:0},Pt)})(this.options.gifBackground);if(E=e,O=!0,L=(N=e).frames.filter((e=>e.image)).map((e=>Mt(e,N.gct,O))),this.options.autoColor){let e=0,t=0,i=0,a=0;for(let s=0;s<L[0].colorTable.length;s++){const o=L[0].colorTable[s];o[0]>200||o[1]>200||o[2]>200||(0===o[0]&&0===o[1]&&0===o[2]||(a++,e+=o[0],t+=o[1],i+=o[2]))}e=~~(e/a),t=~~(t/a),i=~~(i/a),this.options.colorDark=`rgb(${e},${t},${i})`}}else if(this.options.backgroundImage){const e=yield si(this.options.backgroundImage);if(this.options.autoColor){const t=oi._getAverageRGB(e);this.options.colorDark=`rgb(${t.r},${t.g},${t.b})`}M.drawImage(e,0,0,e.width,e.height,0,0,A,A),M.rect(0,0,A,A),M.fillStyle=I,M.fill()}else M.rect(0,0,A,A),M.fillStyle=this.options.colorLight,M.fill();var N,O;const U=$t.getPatternPosition(this.qrCode.typeNumber),R=(null===(i=null===(t=this.options.components)||void 0===t?void 0:t.data)||void 0===i?void 0:i.scale)||ai,$=.5*(1-R);for(let e=0;e<w;e++)for(let t=0;t<w;t++){const i=this.qrCode.isDark(e,t),a=t<8&&(e<8||e>=w-8)||t>=w-8&&e<8;let s=a||(6==e&&t>=8&&t<=w-8||6==t&&e>=8&&e<=w-8);for(let n=1;n<U.length-1;n++)s=s||e>=U[n]-2&&e<=U[n]+2&&t>=U[n]-2&&t<=U[n]+2;const o=t*T+(s?0:$*T),r=e*T+(s?0:$*T);if(B.strokeStyle=i?this.options.colorDark:this.options.colorLight,B.lineWidth=.5,B.fillStyle=i?this.options.colorDark:this.options.colorLight,0===U.length)s||B.fillRect(o,r,(s?1:R)*T,(s?1:R)*T);else{s||t<w-4&&t>=w-4-5&&e<w-4&&e>=w-4-5||B.fillRect(o,r,(s?1:R)*T,(s?1:R)*T)}}const F=U[U.length-1],j=this.options.colorLight;if(B.fillStyle=j,B.fillRect(0,0,8*T,8*T),B.fillRect(0,(w-8)*T,8*T,8*T),B.fillRect((w-8)*T,0,8*T,8*T),(null===(s=null===(a=this.options.components)||void 0===a?void 0:a.timing)||void 0===s?void 0:s.protectors)&&(B.fillRect(8*T,6*T,(w-8-8)*T,T),B.fillRect(6*T,8*T,T,(w-8-8)*T)),(null===(r=null===(o=this.options.components)||void 0===o?void 0:o.cornerAlignment)||void 0===r?void 0:r.protectors)&&oi._drawAlignProtector(B,F,F,T),null===(l=null===(n=this.options.components)||void 0===n?void 0:n.alignment)||void 0===l?void 0:l.protectors)for(let e=0;e<U.length;e++)for(let t=0;t<U.length;t++){const i=U[t],a=U[e];(6!==i||6!==a&&a!==F)&&((6!==a||6!==i&&i!==F)&&(i===F&&a===F||oi._drawAlignProtector(B,i,a,T)))}B.fillStyle=this.options.colorDark,B.fillRect(0,0,7*T,T),B.fillRect((w-7)*T,0,7*T,T),B.fillRect(0,6*T,7*T,T),B.fillRect((w-7)*T,6*T,7*T,T),B.fillRect(0,(w-7)*T,7*T,T),B.fillRect(0,(w-7+6)*T,7*T,T),B.fillRect(0,0,T,7*T),B.fillRect(6*T,0,T,7*T),B.fillRect((w-7)*T,0,T,7*T),B.fillRect((w-7+6)*T,0,T,7*T),B.fillRect(0,(w-7)*T,T,7*T),B.fillRect(6*T,(w-7)*T,T,7*T),B.fillRect(2*T,2*T,3*T,3*T),B.fillRect((w-7+2)*T,2*T,3*T,3*T),B.fillRect(2*T,(w-7+2)*T,3*T,3*T);const z=(null===(d=null===(c=this.options.components)||void 0===c?void 0:c.timing)||void 0===d?void 0:d.scale)||ai,H=.5*(1-z);for(let e=0;e<w-8;e+=2)oi._drawDot(B,8+e,6,T,H,z),oi._drawDot(B,6,8+e,T,H,z);const G=(null===(p=null===(u=this.options.components)||void 0===u?void 0:u.cornerAlignment)||void 0===p?void 0:p.scale)||ai,q=.5*(1-G);oi._drawAlign(B,F,F,T,q,G,this.options.colorDark,(null===(m=null===(h=this.options.components)||void 0===h?void 0:h.cornerAlignment)||void 0===m?void 0:m.protectors)||!1);const V=(null===(f=null===(g=this.options.components)||void 0===g?void 0:g.alignment)||void 0===f?void 0:f.scale)||ai,J=.5*(1-V);for(let e=0;e<U.length;e++)for(let t=0;t<U.length;t++){const i=U[t],a=U[e];(6!==i||6!==a&&a!==F)&&((6!==a||6!==i&&i!==F)&&(i===F&&a===F||oi._drawAlign(B,i,a,T,J,V,this.options.colorDark,(null===(y=null===(v=this.options.components)||void 0===v?void 0:v.alignment)||void 0===y?void 0:y.protectors)||!1)))}if(_&&(B.fillStyle=this.options.backgroundColor,B.fillRect(-x,-x,A,x),B.fillRect(-x,S,A,x),B.fillRect(S,-x,x,A),B.fillRect(-x,-x,x,A)),this.options.logoImage){const e=yield si(this.options.logoImage);let t=this.options.logoScale,i=this.options.logoMargin,a=this.options.logoCornerRadius;(t<=0||t>=1)&&(t=.2),i<0&&(i=0),a<0&&(a=0);const s=S*t,o=.5*(A-s),r=o;B.restore(),B.fillStyle=this.options.logoBackgroundColor,B.save(),oi._prepareRoundedCornerClip(B,o-i,r-i,s+2*i,s+2*i,a+i),B.clip();const n=B.globalCompositeOperation;B.globalCompositeOperation="destination-out",B.fill(),B.globalCompositeOperation=n,B.restore(),B.save(),oi._prepareRoundedCornerClip(B,o,r,s,s,a),B.clip(),B.drawImage(e,o,r,s,s),B.restore(),B.save(),B.translate(x,x)}if(E){let e,t,i,a,s,o;if(L.forEach((function(r){e||(e=new ei(b,b),e.setDelay(r.delay),e.setRepeat(0));const{width:n,height:l}=r.dims;t||(t=new ii(n,l),i=t.getContext("2d"),i.rect(0,0,t.width,t.height),i.fillStyle="#ffffff",i.fill()),a&&o&&n===a.width&&l===a.height||(a=new ii(n,l),s=a.getContext("2d"),o=s.createImageData(n,l)),o.data.set(r.patch),s.putImageData(o,0,0),i.drawImage(a.getContext("2d").canvas,r.dims.left,r.dims.top);const c=new ii(A,A),d=c.getContext("2d");d.drawImage(t.getContext("2d").canvas,0,0,A,A),d.rect(0,0,A,A),d.fillStyle=I,d.fill(),d.drawImage(P.getContext("2d").canvas,0,0,A,A);const u=new ii(b,b),p=u.getContext("2d");p.drawImage(c.getContext("2d").canvas,0,0,b,b),e.addFrame(p.getImageData(0,0,u.width,u.height).data)})),!e)throw new Error("No frames.");if(e.finish(),ri(this.canvas)){const t=e.stream().toFlattenUint8Array().reduce(((e,t)=>e+String.fromCharCode(t)),"");return Promise.resolve(`data:image/gif;base64,${window.btoa(t)}`)}return Promise.resolve(Buffer.from(e.stream().toFlattenUint8Array()))}{M.drawImage(P.getContext("2d").canvas,0,0,A,A),B.drawImage(D.getContext("2d").canvas,-x,-x,A,A);const e=new ii(b,b);e.getContext("2d").drawImage(P.getContext("2d").canvas,0,0,b,b),this.canvas=e;const t=this.options.gifBackground?"gif":"png";return ri(this.canvas)?Promise.resolve(this.canvas.toDataURL(t)):Promise.resolve(this.canvas.toBuffer(t))}}))}}function ri(e){try{return e instanceof HTMLElement}catch(t){return"object"==typeof e&&1===e.nodeType&&"object"==typeof e.style&&"object"==typeof e.ownerDocument}}oi.CorrectLevel=Ot,oi.defaultComponentOptions={data:{scale:.4},timing:{scale:.5,protectors:!1},alignment:{scale:.5,protectors:!1},cornerAlignment:{scale:.5,protectors:!0}},oi.defaultOptions={text:"",size:400,margin:20,colorDark:"#000000",colorLight:"rgba(255, 255, 255, 0.6)",correctLevel:Ot.M,backgroundImage:void 0,backgroundDimming:"rgba(0,0,0,0)",logoImage:void 0,logoScale:.2,logoMargin:4,logoCornerRadius:8,whiteMargin:!0,components:oi.defaultComponentOptions,autoColor:!0,logoBackgroundColor:"#ffffff",backgroundColor:"#ffffff"};const ni={props:{text:{type:String,required:!0},qid:{type:String},correctLevel:{type:Number,default:1},size:{type:Number,default:200},margin:{type:Number,default:20},colorDark:{type:String,default:"#000000"},colorLight:{type:String,default:"#FFFFFF"},bgSrc:{type:String,default:void 0},background:{type:String,default:"rgba(0,0,0,0)"},backgroundDimming:{type:String,default:"rgba(0,0,0,0)"},logoSrc:{type:String,default:void 0},logoBackgroundColor:{type:String,default:"rgba(255,255,255,1)"},gifBgSrc:{type:String,default:void 0},logoScale:{type:Number,default:.2},logoMargin:{type:Number,default:0},logoCornerRadius:{type:Number,default:8},whiteMargin:{type:[Boolean,String],default:!0},dotScale:{type:Number,default:1},autoColor:{type:[Boolean,String],default:!0},binarize:{type:[Boolean,String],default:!1},binarizeThreshold:{type:Number,default:128},callback:{type:Function,default:function(){}},bindElement:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},components:{default:function(){return{data:{scale:1},timing:{scale:1,protectors:!1},alignment:{scale:1,protectors:!1},cornerAlignment:{scale:1,protectors:!0}}}}},name:"vue-qr",data:()=>({imgUrl:""}),watch:{$props:{deep:!0,handler(){this.main()}}},mounted(){this.main()},methods:{async main(){if(this.gifBgSrc){const t=await(e=this.gifBgSrc,new Promise(((t,i)=>{var a=new XMLHttpRequest;a.responseType="blob",a.onload=function(){var e=new FileReader;e.onloadend=function(){t(e.result)},e.readAsArrayBuffer(a.response)},a.open("GET",e),a.send()}))),i=this.logoSrc;return void this.render(void 0,i,t)}var e;const t=this.bgSrc,i=this.logoSrc;this.render(t,i)},async render(e,t,i){const a=this;new oi({gifBackground:i,text:a.text,size:a.size,margin:a.margin,colorDark:a.colorDark,colorLight:a.colorLight,backgroundColor:a.backgroundColor,backgroundImage:e,backgroundDimming:a.backgroundDimming,logoImage:t,logoScale:a.logoScale,logoBackgroundColor:a.logoBackgroundColor,correctLevel:a.correctLevel,logoMargin:a.logoMargin,logoCornerRadius:a.logoCornerRadius,whiteMargin:$e(a.whiteMargin),dotScale:a.dotScale,autoColor:$e(a.autoColor),binarize:$e(a.binarize),binarizeThreshold:a.binarizeThreshold,components:a.components}).draw().then((e=>{this.imgUrl=e,a.callback&&a.callback(e,a.qid)}))}}},li=["src"];const ci=l(ni,[["render",function(e,t,i,a,s,o){return i.bindElement?(c(),d("img",{key:0,style:{display:"inline-block"},src:s.imgUrl},null,8,li)):u("",!0)}]]),di={id:"app"},ui={class:"scale"},pi={class:"micro_header"},hi={class:"micro_left"},mi={class:"avatar"},gi=["src"],fi={class:"info"},vi={class:"t1"},yi={class:"micro_main"},wi={class:"micro_main_top"},bi={class:"micro_main-sp"},ki={class:"micro_main_temp"},xi=["onClick"],Ci={class:"title"},_i={class:"price"},Ii={class:"micro_main_middle"},Ti=["src"],Si={key:0,class:"micro_main_bottom"},Ai={class:"micro_pay"},Pi={class:"micro_pay_right"},Bi={class:"noQrCode"},Di={class:"price"},Mi={class:"micro_way"},Ei=(e=>(m("data-v-716bcc03"),e=e(),g(),e))((()=>p("div",{class:"box"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})],-1))),Li={class:"t1"},Ni={class:"bd"},Oi={class:"t2"},Ui={key:1,class:"btns"},Ri={key:2,class:"btns"},$i=l({__name:"index",props:{userInfo:{type:Object,default:()=>({})},currentItem:{type:Object,default:()=>({})}},emits:["close"],setup(e,{emit:t}){const{t:i}=f(),s=a(!1),r=v(),n=((null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location||location.origin.includes("medsci.cn")),a(!1)),l=e,m=l.userInfo||{},g=l.currentItem,U=a({}),R=a(),$=t,F=a(""),j=a(),z=a(),H=a(),G=a(localStorage.getItem("socialType")),q=a((null==m?void 0:m.avatar)?null==m?void 0:m.avatar:"https://img.medsci.cn/web/img/user_icon.png"),V=()=>{q.value="https://img.medsci.cn/web/img/user_icon.png"},J=()=>{window.open("https://www.medsci.cn/about/index.do?id=27")},W=()=>{const e=window.innerWidth;n.value=e>768},Q=()=>{clearInterval(H.value),$("close")},K=(e,t)=>{U.value=e,R.value=t,0==G.value&&0!=e.feePrice&&X(e,g.appUuid)};y((()=>{clearInterval(H.value)}));const X=async(e,t)=>{if(!U.value.coinType)return void B.warning("请选择订阅服务周期");let a=await D();if(null==m?void 0:m.userId){const a={appUuid:t,priceId:e.priceId,monthNum:e.monthNum};try{s.value=!0;let t=await M(a);if(t)if(s.value=!1,0==G.value&&0!=e.feePrice){const e=t;location.origin.includes(".medsci.cn")||location.origin.includes(".medon.com.cn")?F.value=location.origin+"/apps/payLink/"+encodeURIComponent(e):F.value=location.origin+"/payLink/"+encodeURIComponent(e),z.value=JSON.parse(e).piId,await void(H.value=setInterval((async()=>{"PAID"===(await O(z.value)).payStatus&&(location.reload(),clearInterval(H.value))}),2e3))}else B({type:"success",message:i("tool.sS")}),setTimeout((()=>{location.href=t}),1e3)}catch(o){s.value=!1}}else a&&"zh-CN"!=a?r.push("/login"):window.addLoginDom()};return w((()=>{window.removeEventListener("resize",W)})),b((()=>{"写作"==g.appType&&localStorage.setItem("appWrite",JSON.stringify({appUuid:g.appUuid,directoryMd:g.directoryMd})),W(),window.addEventListener("resize",W),(async()=>{const e=await E("homePayImg");j.value=e.list[0].value})(),n.value&&1==g.feeTypes.length&&(g.feeTypes[0].feePrice>0&&0==G.value&&K(g.feeTypes[0],0),g.feeTypes[0].feePrice>0&&0!=G.value&&K(g.feeTypes[0],0),0==g.feeTypes[0].feePrice&&K(g.feeTypes[0],0))})),(e,t)=>{var i,a,r,n,l,f,v,y,w,b;const B=L,D=N;return c(),d("div",di,[p("div",ui,[p("div",pi,[p("div",hi,[p("div",mi,[p("img",{src:k(q),onError:V,alt:""},null,40,gi)]),p("div",fi,[p("span",vi,h((null==(i=k(m))?void 0:i.realName)?null==(a=k(m))?void 0:a.realName:null==(r=k(m))?void 0:r.userName),1)])]),p("div",{class:"micro_right"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png",alt:"",onClick:Q})])]),p("div",yi,[p("div",wi,[p("div",bi,[p("div",ki,[0!=k(G)||k(g).feeTypes.length>1?(c(),d("div",{key:0,class:"swiper-vip",showIndicator:!1,autoPlay:!1,style:x({transform:`translate(${e.translateVipVal}px)`})},[(c(!0),d(C,null,_(k(g).feeTypes,((t,i)=>(c(),d("div",{class:"swiper-vip-item",key:i,onClick:e=>K(t,i)},[p("div",{class:"newer",style:x({left:i%4==0&&0!=i?"6px":"-1px"})},null,4),p("div",{class:I(["swiper-vip-item-child",{sactvie:k(R)==i}])},[p("div",Ci,h(e.$t(`tool.${t.type}`)),1),p("div",_i,h("人民币"==t.coinType?"¥":"$")+h(t.feePrice),1)],2)],8,xi)))),128))],4)):u("",!0)])])]),p("div",Ii,[p("img",{class:"vip-banner",alt:"",src:k(j)},null,8,Ti)]),(null==(n=k(U))?void 0:n.coinType)&&0!=k(U).feePrice&&0==k(G)?(c(),d("div",Si,[p("div",Ai,[p("div",Pi,[T(p("div",Bi,null,512),[[D,k(s)],[S,k(s)]]),T(o(ci,{ref:"qrcode",class:"qr-code",id:"qrcode",correctLevel:3,autoColor:!1,colorDark:"#000000",text:k(F),size:95,margin:0,logoMargin:3},null,8,["text"]),[[S,!k(s)]]),p("div",Di,[p("div",Mi,[Ei,p("span",null,h(e.$t("tool.Support_Alipay_Payment")),1)]),p("span",Li,[A(h(e.$t("tool.Support_Alipay_Payment")),1),p("span",Ni,h(null==(l=k(U))?void 0:l.feePrice),1),A(h("人民币"==(null==(f=k(U))?void 0:f.coinType)?"¥":"$")+"/"+h(3==(null==(v=k(U))?void 0:v.monthNum)?e.$t("tool.Quarter"):12==(null==(y=k(U))?void 0:y.monthNum)?e.$t("tool.Year"):e.$t("tool.Month")),1)]),p("span",Oi,h(e.$t("tool.Meisi_Account"))+"："+h(k(m).userName),1),p("span",{class:"t3",onClick:J},[A(h(e.$t("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"))+" ",1),p("img",{onClick:J,src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})])])])])])):u("",!0),(null==(w=k(U))?void 0:w.coinType)&&0==k(U).feePrice?(c(),d("div",Ui,[o(B,{type:"primary",onClick:t[0]||(t[0]=e=>X(k(U),k(g).appUuid))},{default:P((()=>[A(h(e.$t("tool.Free_Trial")),1)])),_:1})])):u("",!0),(null==(b=k(U))?void 0:b.coinType)&&k(U).feePrice>0&&0!=k(G)?(c(),d("div",Ri,[o(B,{type:"primary",onClick:t[1]||(t[1]=e=>X(k(U),k(g).appUuid))},{default:P((()=>[A(h(e.$t("market.subscribe")),1)])),_:1})])):u("",!0)])])])}}},[["__scopeId","data-v-716bcc03"]]),Fi={name:"Vip",data:()=>({isCheckW:!0,isCheckZ:!1,isLogin:!1,activeItem:{},appId:"wx9096048917ec59ab",appOrderId:"",isClick:!1,openId:"",isWx:!1,choseUserVip:{},isFromMedsci:!1,showAll:!1,checkCount:0,vipTypeList:[],activeType:0,active:0,radio:"",isShaking:!1,avatar:"",socialType:localStorage.getItem("socialType")}),components:{VanCheckbox:Ae},props:{userInfo:{type:Object,default:()=>({})},currentItem:{type:Object,default:()=>({})}},created(){},mounted(){var e,t;"写作"==this.currentItem.appType&&localStorage.setItem("appWrite",JSON.stringify({appUuid:this.currentItem.appUuid,directoryMd:this.currentItem.directoryMd})),this.avatar=(null==(e=this.userInfo)?void 0:e.avatar)?null==(t=this.userInfo)?void 0:t.avatar:"https://img.medsci.cn/web/img/user_icon.png",this.isUp=(null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location?void 0:location.origin.includes("medsci.cn")),1==this.currentItem.feeTypes.length&&(this.currentItem.feeTypes[0].feePrice>0&&0==localStorage.getItem("socialType")&&this.CheckItem(this.currentItem.feeTypes[0],this.currentItem.appUuid),0==this.currentItem.feeTypes[0].feePrice&&this.CheckItem(this.currentItem.feeTypes[0],this.currentItem.appUuid)),U.get("userInfo")&&JSON.parse(U.get("userInfo")).userId&&(this.isLogin=!0,this.initUser()),this.init(),this.$route.query.source&&"medsci"==this.$route.query.source&&(this.isFromMedsci=!0)},methods:{changeImg(){this.avatar="https://img.medsci.cn/web/img/user_icon.png"},async subscribe(e,t,i){var a;if(!this.radio&&i)return this.isShaking=!0,void setTimeout((()=>{this.isShaking=!1}),500);let s=await D();if(null==(a=this.userInfo)?void 0:a.userId){const i={appUuid:t,priceId:e.priceId,monthNum:e.monthNum};try{let t=await M(i);if(t)if(0==localStorage.getItem("socialType")&&0!=e.feePrice){let e=await R(JSON.parse(t));B({type:"success",message:this.$t("tool.sS")}),setTimeout((()=>{location.href=e}),1e3)}else B({type:"success",message:this.$t("tool.sS")}),setTimeout((()=>{location.href=t}),1e3)}catch(o){}}else s&&"zh-CN"!=s?this.$router.push((this.isUp,"/login")):window.addLoginDom()},CheckItem(e,t){this.activeItem=e,this.active=t},openActivity(e){e&&(window.location.href=e)},login(){addLoginDom()},initUser(){},init(){},isMedSci:()=>navigator.userAgent.includes("medsci_app"),goBack(){window.history.back(-1)},checkFn1(){this.isCheckW=!0,this.isCheckZ=!1},checkFn2(){this.isCheckW=!1,this.isCheckZ=!0},goAgreent(){const e="https://portal-test.medon.com.cn/agreement/27";this.isMedSci()?window.location.href=e:window.open(e)},createOrder(){this.isWx&&(this.isCheckW=!0,this.isCheckZ=!1);const e={accessAppId:"college",appOrderId:this.appOrderId,payChannel:this.isCheckW?"WX":"ALI",paySource:"MEDSCI_WEB",payType:this.isWx?"JSAPI":"MWEB"};this.$axios.post(api.payBuild,e).then((e=>{this.orderList(e.data.payOrderId)}))},orderList(e){const t={};this.$route.query.from&&(t.from="app"),this.isFromMedsci&&(t.sourcefrom="main",t.redirectUrl=this.$route.query.redirectUrl);const i={accessAppId:"college",openId:this.isWx?this.openId:"",payOrderId:e,extParam:JSON.stringify(t)};this.$axios.post(api.payOrder,i).then((e=>{if(this.isCheckW){if(this.isWx)return void this.wxOrder(e.data.wechatJsapi);window.location.href=e.data.wechatH5.h5Url}else if(e.data.aliH5.html){const t=document.createElement("div");t.innerHTML=e.data.aliH5.html,document.body.appendChild(t),document.forms[0].submit()}}))},wxOrder(e){WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:e.appId,timeStamp:e.timeStamp,nonceStr:e.nonceStr,package:e.packageStr,signType:e.signType,paySign:e.paySign},(function(e){"get_brand_wcpay_request:ok"==e.err_msg?(ye.success("支付成功！"),setTimeout((()=>{window.location.reload()}),1e3)):e.err_msg}))}}},ji=e=>(m("data-v-6f121840"),e=e(),g(),e),zi={key:0,class:"vip-head"},Hi={class:"vip-introduce"},Gi=ji((()=>p("img",{class:"crown",src:"https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png",alt:""},null,-1))),qi={class:"box"},Vi={key:0,class:"box-left-1"},Ji=ji((()=>p("img",{src:"https://img.medsci.cn/web/img/user_icon.png",alt:""},null,-1))),Wi={class:"left2"},Qi=ji((()=>p("span",{class:"t2"},"请登录后购买",-1))),Ki={key:1,class:"box-left"},Xi=["src"],Yi={class:"box-word"},Zi={class:"t1"},ea={class:"vip-main"},ta={class:"vip-one"},ia={class:"big"},aa={ref:"scroll"},sa=["onClick"],oa={class:"title ellipsis-2-lines"},ra={class:"price"},na={key:0,class:"isfava"},la={key:0,class:"vip-two"},ca=ji((()=>p("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:""},null,-1))),da=[ji((()=>p("div",{class:"item-left"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""}),p("span",null,"支付宝支付")],-1))),ji((()=>p("div",{class:"item-right isCheck"},[p("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:""})],-1)))],ua={key:1,class:"vip-pay btns"},pa={key:0,class:"pay-left"},ha={class:"t1"},ma=ji((()=>p("span",null,"协议",-1)));const ga=l(Fi,[["render",function(e,t,i,a,s,r){const n=$("van-checkbox"),l=L;return c(),d("div",{class:I(["vip",{sp:r.isMedSci()}])},[r.isMedSci()?u("",!0):(c(),d("div",zi,h(i.currentItem.appName),1)),p("div",Hi,[Gi,p("div",qi,[s.isLogin?(c(),d("div",Ki,[p("img",{class:"avatar",src:s.avatar,alt:"",onError:t[1]||(t[1]=(...e)=>r.changeImg&&r.changeImg(...e))},null,40,Xi),p("div",Yi,[p("span",Zi,h(i.userInfo.realName||i.userInfo.userName),1)])])):(c(),d("div",Vi,[Ji,p("div",Wi,[p("span",{class:"t1",style:{cursor:"pointer"},onClick:t[0]||(t[0]=(...e)=>r.login&&r.login(...e))},"立即登录"),Qi])]))])]),p("div",ea,[p("div",ta,[p("div",ia,[p("ul",aa,[(c(!0),d(C,null,_(i.currentItem.feeTypes,((t,i)=>(c(),d("li",{key:i,class:I({sactvie:t.type==s.activeItem.type}),onClick:e=>r.CheckItem(t)},[p("div",oa,h(e.$t(`tool.${t.type}`)),1),p("div",ra,"¥"+h(t.feePrice),1),t.originalPrice?(c(),d("div",na,h("人民币"==t.coinType?"¥":"$")+h(t.feePrice),1)):u("",!0)],10,sa)))),128))],512)])]),s.activeItem.feePrice>0&&0==s.socialType?(c(),d("div",la,[p("div",{class:I(["pay",{isWx:s.isWx}])},[ca,p("div",{class:"item",onClick:t[2]||(t[2]=(...e)=>r.checkFn2&&r.checkFn2(...e))},da)],2)])):u("",!0)]),s.activeItem.feePrice>=0?(c(),d("div",ua,[0!=s.activeItem.feePrice&&0==s.socialType?(c(),d("div",pa,[p("div",ha,h(i.currentItem.appName),1),p("div",{class:I(["t2",{shake:s.isShaking}])},[o(n,{modelValue:s.radio,"onUpdate:modelValue":t[3]||(t[3]=e=>s.radio=e)},null,8,["modelValue"]),p("span",{onClick:t[4]||(t[4]=(...e)=>r.goAgreent&&r.goAgreent(...e))},[A("请在阅读并同意"),ma,A("后开通")])],2)])):u("",!0),0!=s.activeItem.feePrice&&0==s.socialType?(c(),d("div",{key:1,class:"pay-right",onClick:t[5]||(t[5]=e=>r.subscribe(s.activeItem,i.currentItem.appUuid,"ali"))},[p("span",null,h(s.activeItem.feePrice)+"元确认协议并支付",1)])):u("",!0),0==s.activeItem.feePrice?(c(),F(l,{key:2,onClick:t[6]||(t[6]=e=>r.subscribe(s.activeItem,i.currentItem.appUuid)),type:"primary"},{default:P((()=>[A(h(e.$t("tool.Free_Trial")),1)])),_:1})):u("",!0),s.activeItem.feePrice>0&&0!=s.socialType?(c(),F(l,{key:3,onClick:t[7]||(t[7]=e=>r.subscribe(s.activeItem,i.currentItem.appUuid)),type:"primary"},{default:P((()=>[A(h(e.$t("market.subscribe")),1)])),_:1})):u("",!0)])):u("",!0)],2)}],["__scopeId","data-v-6f121840"]]);function fa(e,t){const i=Date.now();localStorage.setItem(e+"_value",t),localStorage.setItem(e+"_timestamp",i)}function va(e,t){const i=e+"_value",a=e+"_timestamp",s=localStorage.getItem(i),o=localStorage.getItem(a);if(null!==s&&null!==o){const e=new Date(o);return(new Date-e)/864e5>t?(localStorage.removeItem(i),localStorage.removeItem(a),null):s}return null}function ya(){let e=va("current_langs_pack",7),t=va("current_langs_pack_umo",7);if(!e||!t){fetch("https://ai.medon.com.cn/dev-api/ai-base/index/getConfigPage").then((e=>{if(!e.ok)throw new Error("Network response was not ok");return e.json()})).then((t=>{if(0!==t.data.list.length){e=JSON.stringify(function(e){const t={};return e.forEach((e=>{const[i]=e.key.split("."),a=JSON.parse(e.value);t[i]||(t[i]={}),t[i]={...t[i],...a}})),t}(t.data.list)),fa("current_langs_pack",e);let i=t.data.list.filter((e=>"{}"!=e.value&&e.key.includes(".dify"))).reduce(((e,t)=>(e[t.key.substr(0,t.key.indexOf("."))]||(e[t.key.substr(0,t.key.indexOf("."))]={}),e[t.key.substr(0,t.key.indexOf("."))]=JSON.parse(t.value),e)),{});fa("current_langs_pack_umo",JSON.stringify(i))}})).catch((e=>{}))}}const wa={class:"bg-[#F9F9F9] overflow-auto"},ba={class:"pt-[75px] text-white mb-[30px] font-bold"},ka={class:"flex justify-center"},xa=(e=>(m("data-v-da22a355"),e=e(),g(),e))((()=>p("img",{class:"w-[24px] h-[24px]",src:"/apps/static/搜索-8381f2c3.svg",alt:""},null,-1))),Ca={class:"content"},_a={class:"flex justify-center my-8 bg-[#F9F9F9]"},Ia={class:"flex items-center"},Ta=["onClick"],Sa={key:0,class:"menu-box flex flex-wrap justify-between"},Aa={class:"flex mb-1 card-item"},Pa={class:"flex",style:{width:"75%","align-items":"center"}},Ba=["src"],Da=["title","innerHTML"],Ma={style:{width:"30%","text-align":"right","font-size":"14px"}},Ea=["title","innerHTML"],La={class:"flex justify-between items-center"},Na={class:"text-[#B0B0B0]"},Oa={key:0,class:"during_order"},Ua={key:1,class:"delay_order"},Ra={key:1,class:"tab_box"},$a={class:"menu-box flex flex-wrap justify-between"},Fa={class:"flex mb-1 card-item"},ja={class:"flex",style:{width:"75%","align-items":"center"}},za=["src"],Ha=["title","innerHTML"],Ga={style:{width:"30%","text-align":"right"}},qa=["innerHTML"],Va={class:"flex justify-between items-center"},Ja={class:"text-[#B0B0B0]"},Wa={key:0,class:"during_order"},Qa={key:1,class:"delay_order"},Ka=l({__name:"index",setup(i){const{t:s}=f(),r=((null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location||location.origin.includes("medsci.cn")),be("基于AI的写作文本加工.png")),n=j(),l=v(),m=a(""),g=a([]),y=a([{value:"我的应用",remark:"我的应用"},{value:"",remark:"全部"}]),w=a(!1),T=a(1),S=a(null),E=a(null),N=a("first"),O=a(null),R=a(!1),$=a({appType:"",socialUserId:"",appLang:"",order:2,isMine:2}),oe=()=>{R.value=!1},re=async(e,t)=>{var i;let a=D();if(null==(i=E.value)?void 0:i.userId){const i={appUuid:t,priceId:e.priceId,monthNum:e.monthNum};let a=await M(i);a&&(B({type:"success",message:s("tool.sS")}),setTimeout((()=>{location.href=a}),1e3))}else a&&"zh-CN"!=a?l.push("/login"):window.addLoginDom()},ne=e=>{var t;1==(null==(t=e.appUser)?void 0:t.status)?ce(e):ve(e)},le=e=>{let t=[],i=[];1==T.value?t=JSON.parse(JSON.stringify(de)):0!=T.value?t=JSON.parse(JSON.stringify(de)).filter((e=>e.appType===y.value[T.value].value)):0==T.value&&(t=JSON.parse(JSON.stringify(g.value))),i=t.filter((t=>{if(t.appName.includes(e)||t.appDescription.includes(e)||t.mapType.includes(e))return t})),g.value.forEach((e=>{e.appName=e.appName.replace(/<[^>]+>/g,""),e.appDescription=e.appDescription.replace(/<[^>]+>/g,""),e.mapType=e.mapType.replace(/<[^>]+>/g,"")}));let a=new RegExp(e,"gi");g.value=i.map((t=>(e&&(t.appName=t.appName.replace(a,`<span style="color: #409eff">${e}</span>`),t.appDescription=t.appDescription.replace(a,`<span style="color: #409eff">${e}</span>`),t.mapType=t.mapType.replace(a,`<span style="color: #409eff">${e}</span>`)),t)))},ce=async e=>{if(!(null==e?void 0:e.dAppUuid))return void B({message:"请先至后台绑定应用实例",type:"warning"});Y(e.appUuid,localStorage.getItem("openid")),sessionStorage.setItem("nodeInfo",JSON.stringify(e));const t=window.location.href.replace(/\/$/,"");"工具"!==e.appType?"问答"!==e.appType?"写作"===e.appType&&(await ya(),localStorage.setItem("appWrite",JSON.stringify({appUuid:e.appUuid,directoryMd:e.directoryMd})),window.open(`${window.location.origin}/ai-write/write/${e.appUuid}?appName=${e.appName}`)):window.open(`${t}/chat/${null==e?void 0:e.dAppUuid}/${null==e?void 0:e.appUuid}?appName=${e.appName}`,"_blank"):window.open(`${t}/tool/${null==e?void 0:e.dAppUuid}/${null==e?void 0:e.appUuid}?appName=${e.appName}`,"_blank")};let de=[];const ue=a(["https://img.medsci.cn/medsci-ai/bg1.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg2.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg3.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg4.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg5.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg6.png?imageMogr2/format/webp"]);b((async()=>{var e,t,i;const a=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${a}px`);["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=n.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(t=n.params)?void 0:t.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),"zh-CN"==localStorage.getItem("ai_apps_lang")&&(w.value=!0),E.value=U.get("userInfo")?JSON.parse(U.get("userInfo")):null,E.value||(localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken")),n.query.lang?$.value.appLang=H[n.query.lang]:$.value.appLang=z();let s=Math.floor(6*Math.random());S.value=ue.value[s],(null==(i=E.value)?void 0:i.userId)?($.value.socialUserId=E.value.plaintextUserId,$.value.appLang=z()||location.pathname.replaceAll("/",""),ge(),fe()):($.value.socialUserId=0,z()?$.value.appLang=z():me(location.pathname.replaceAll("/","")),ge()),await pe(),(async()=>{var e,t;let i=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=E.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:"",userUuid:null==(t=E.value)?void 0:t.openid}];await X.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",i)})()}));const pe=()=>{G().then((e=>{y.value.push(...e)})).catch()},he=e=>{w.value=e},me=e=>{$.value.appLang=H[e],ge()},ge=()=>{q($.value).then((e=>{var t,i;g.value=null==e?void 0:e.map((e=>({...e,mapType:V[e.appType]}))),""==$.value.appType&&(de=[...g.value]),1==$.value.isMine&&("first"==N.value&&(g.value=null==(t=g.value)?void 0:t.filter((e=>{var t;return 1==(null==(t=e.appUser)?void 0:t.status)}))),"second"==N.value&&(g.value=null==(i=g.value)?void 0:i.filter((e=>{var t;return 2==(null==(t=e.appUser)?void 0:t.status)}))))})).catch((e=>{}))},fe=()=>{if(localStorage.getItem("yudaoToken"))return void ge();const e=U.get("userInfo");if(e){const i=JSON.parse(e);try{J({userId:i.userId,userName:i.userName,realName:i.realName,avatar:i.avatar,plaintextUserId:i.plaintextUserId,mobile:i.mobile,email:i.email}).then((e=>{(null==e?void 0:e.token)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),ge())}))}catch(t){}}},ve=async e=>{O.value=e,R.value=!0},ye=()=>{ge()};return(i,a)=>{const s=e,n=Z,f=ee,v=L,b=te,B=ie,M=ae,U=Re,j=se;return c(),d("div",wa,[o(s,{onGetAppLang:me,onIsZHChange:he}),p("div",{class:"flex flex-col items-center h-[246px] relative min-w-[980px]",style:x({background:`url(${k(S)}) no-repeat center`,backgroundSize:"cover"})},[p("h1",ba,h(i.$t("faq.xAI")),1),p("div",ka,[o(f,{class:"!w-[888px] !h-[54px]",modelValue:k(m),"onUpdate:modelValue":a[0]||(a[0]=e=>W(m)?m.value=e:null),placeholder:i.$t("market.keywords"),clearable:"",onInput:le},{prefix:P((()=>[o(n,{size:"24",class:"cursor-pointer mt-[2px]"},{default:P((()=>[xa])),_:1})])),_:1},8,["modelValue","placeholder"])])],4),p("main",null,[p("div",Ca,[p("div",_a,[p("div",Ia,[(c(!0),d(C,null,_(k(y),((e,t)=>(c(),d("div",{class:I(["mr-2 px-4 py-1 cursor-pointer m_font",k(T)==t?"bg-[#409eff] text-white rounded-4xl":""]),key:t,onClick:i=>(async(e,t)=>{var i;let a=await D();if(T.value=e,m.value="",m.value&&le(m.value),!(null==(i=E.value)?void 0:i.userId)&&0==T.value)return g.value=[],void(a&&"zh-CN"!=a?l.push("/login"):window.addLoginDom());0!=T.value?($.value.isMine=2,$.value.order=2,"全部"==t.remark?$.value.appType="":$.value.appType=t.value):(N.value="first",$.value.appType="",$.value.isMine=1,$.value.order=1),ge()})(t,e)},h(i.$t(`${k(V)[e.remark]}`)),11,Ta)))),128))])]),0!=k(T)?(c(),d("div",Sa,[(c(!0),d(C,null,_(k(g),((e,t)=>(c(),F(b,{shadow:"hover",class:"max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item",style:x({background:`url(${k(r)}) no-repeat center`,backgroundSize:"cover",width:"calc(33.33% - 8px)"}),key:t,onClick:t=>ne(e)},{default:P((()=>{var t,a,s,r,l,m;return[p("div",Aa,[p("div",Pa,[p("img",{class:"w-[40px] h-[40px] block mr-2",style:{"border-radius":"10px"},src:e.appIcon,alt:"icon"},null,8,Ba),p("div",{class:"text-[16px] font-bold text-dark-200 two_lines",style:{width:"calc(100% - 40px)"},title:e.appName,innerHTML:e.appName},null,8,Da)]),p("div",Ma,[1==(null==(t=e.appUser)?void 0:t.status)?(c(),F(v,{key:0,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#2F92EE",plain:"",round:"",onClick:Q((t=>ce(e)),["stop"])},{default:P((()=>[A(h(i.$t("market.open")),1),o(n,null,{default:P((()=>[o(k(K),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])):u("",!0),2==(null==(a=e.appUser)?void 0:a.status)?(c(),F(v,{key:1,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:Q((t=>ve(e)),["stop"])},{default:P((()=>[A(h(i.$t("market.renew")),1),o(n,null,{default:P((()=>[o(k(K),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])):u("",!0),e.appUser?u("",!0):(c(),F(v,{key:2,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:Q((t=>ve(e)),["stop"])},{default:P((()=>[A(h(i.$t("market.subscribe")),1),o(n,null,{default:P((()=>[o(k(K),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"]))])]),p("div",{class:"textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]",title:e.appDescription,innerHTML:e.appDescription},null,8,Ea),p("div",La,[p("div",Na,h(i.$t(`${k(V)[e.appType]}`)),1),1==(null==(s=e.appUser)?void 0:s.status)?(c(),d("div",Oa,h(i.$t("market.subUntil"))+h(null==(r=e.appUser)?void 0:r.expireAt)+h(i.$t("market.expiredOn")),1)):u("",!0),2==(null==(l=e.appUser)?void 0:l.status)?(c(),d("div",Ua,h(i.$t("market.haveBeen"))+h(null==(m=e.appUser)?void 0:m.expireAt)+h(i.$t("market.expiredOn")),1)):u("",!0)])]})),_:2},1032,["style","onClick"])))),128))])):(c(),d("div",Ra,[o(M,{modelValue:k(N),"onUpdate:modelValue":a[1]||(a[1]=e=>W(N)?N.value=e:null),class:"demo-tabs",onTabChange:ye},{default:P((()=>[o(B,{label:i.$t("market.subscribed"),name:"first"},null,8,["label"]),o(B,{label:i.$t("market.expired"),name:"second"},null,8,["label"])])),_:1},8,["modelValue"]),p("div",$a,[(c(!0),d(C,null,_(k(g),((e,t)=>(c(),F(b,{shadow:"hover",class:"max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item",style:x({background:`url(${k(r)}) no-repeat center`,backgroundSize:"cover",width:"calc(33.33% - 8px)",maxHeight:"189.5px"}),key:t,onClick:t=>ne(e)},{default:P((()=>{var t,a,s,r,l,m;return[p("div",Fa,[p("div",ja,[p("img",{class:"w-[40px] h-[40px] block mr-2",style:{"border-radius":"10px"},src:e.appIcon,alt:"icon"},null,8,za),p("div",{class:"text-[16px] font-bold text-dark-200 two_lines",style:{width:"calc(100% - 40px)"},title:e.appName,innerHTML:e.appName},null,8,Ha)]),p("div",Ga,[1==(null==(t=e.appUser)?void 0:t.status)?(c(),F(v,{key:0,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#2F92EE",plain:"",round:"",onClick:Q((t=>ce(e)),["stop"])},{default:P((()=>[A(h(i.$t("market.open")),1),o(n,null,{default:P((()=>[o(k(K),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])):u("",!0),2==(null==(a=e.appUser)?void 0:a.status)?(c(),F(v,{key:1,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:Q((t=>ve(e)),["stop"])},{default:P((()=>[A(h(i.$t("market.renew")),1),o(n,null,{default:P((()=>[o(k(K),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])):u("",!0),e.appUser?u("",!0):(c(),F(v,{key:2,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:Q((t=>ve(e)),["stop"])},{default:P((()=>[A(h(i.$t("market.subscribe")),1),o(n,null,{default:P((()=>[o(k(K),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"]))])]),p("div",{class:"textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]",innerHTML:e.appDescription},null,8,qa),p("div",Va,[p("div",Ja,h(i.$t(`${k(V)[e.appType]}`)),1),1==(null==(s=e.appUser)?void 0:s.status)?(c(),d("div",Wa,h(i.$t("market.subUntil"))+h(null==(r=e.appUser)?void 0:r.expireAt)+h(i.$t("market.expiredOn")),1)):u("",!0),2==(null==(l=e.appUser)?void 0:l.status)?(c(),d("div",Qa,h(i.$t("market.haveBeen"))+h(null==(m=e.appUser)?void 0:m.expireAt)+h(i.$t("market.expiredOn")),1)):u("",!0)])]})),_:2},1032,["style","onClick"])))),128))])]))])]),k(w)?(c(),F(t,{key:0})):u("",!0),o(U,{class:"mobile_footer"}),k(R)?(c(),F(j,{key:1,modelValue:k(R),"onUpdate:modelValue":a[2]||(a[2]=e=>W(R)?R.value=e:null),class:"payPC","show-close":!1},{default:P((()=>[o($i,{userInfo:k(E),appTypes:k(V),currentItem:k(O),onToAgreement:i.toAgreement,onClose:oe,onSubscribe:re},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["modelValue"])):u("",!0),o(k(we),{show:k(R),"onUpdate:show":a[3]||(a[3]=e=>W(R)?R.value=e:null),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"80%"}},{default:P((()=>[o(ga,{userInfo:k(E),appTypes:k(V),currentItem:k(O),onToAgreement:i.toAgreement,onClose:oe},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["show"])])}}},[["__scopeId","data-v-da22a355"]]);export{Ka as default};
