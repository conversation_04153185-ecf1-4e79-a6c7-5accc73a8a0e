/* empty css                  *//* empty css                     *//* empty css                 */import{a as e,u as l,V as o,r as a,N as s,x as t,o as i,aA as n,c,d as r,i as u,t as d,e as m,l as p,j as g,aB as v,a3 as _,aG as f,aH as b,B as I,aD as h,E as B,aE as k,aF as y,ae as x,af as w}from"./index-732573c8.js";import{_ as T}from"./_plugin-vue_export-helper-1b428a4d.js";const $=e=>(x("data-v-e392a1cf"),e=e(),w(),e),S={class:"bg-background text-foreground antialiased min-h-screen flex items-center justify-center"},U={class:"cl-rootBox cl-signUp-root justify-center"},j={class:"cl-cardBox cl-signUp-start"},C={class:"cl-card cl-signUp-start"},V={class:"cl-header"},N={class:"cl-headerTitle"},A={class:"cl-headerSubtitle"},q={class:"cl-main"},z={class:"cl-socialButtonsRoot"},R={class:"cl-socialButtons"},L={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},O={class:"cl-socialButtonsBlockButton-d"},P=$((()=>u("span",null,[u("img",{src:"https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1))),G={class:"cl-socialButtons"},J={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},E={class:"cl-socialButtonsBlockButton-d"},F=$((()=>u("span",null,[u("img",{src:"https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1))),H=$((()=>u("div",{class:"cl-dividerRow"},[u("div",{class:"cl-dividerLine"}),u("p",{class:"cl-dividerText"},"or"),u("div",{class:"cl-dividerLine"})],-1))),W={class:"cl-socialButtonsRoot"},Z={class:"cl-internal-1pnppin"},M=$((()=>u("div",{id:"clerk-captcha",class:"cl-internal-3s7k9k"},null,-1))),D={class:"cl-internal-742eeh"},Q={class:"cl-internal-2iusy0"},K=$((()=>u("svg",{class:"cl-buttonArrowIcon 🔒️ cl-internal-1c4ikgf"},[u("path",{fill:"currentColor",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"m7.25 5-3.5-2.25v4.5L7.25 5Z"})],-1))),X={class:"cl-footer 🔒️ cl-internal-4x6jej"},Y={class:"cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70"},ee={class:"cl-footerActionText cl-internal-kyvqj0","data-localization-key":"signUp.start.actionText"},le=T({__name:"index",setup(x){var w;const{t:T}=e(),$=l(),le=o(),oe=$.params.socialType,ae=$.query.authCode,se=$.query.authState,te=a(),ie=a({email:"",password:"",emailCode:"",userName:""}),ne=a((null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location?void 0:location.origin.includes("medsci.cn"))),ce=s({userName:[{required:!0,message:T("tool.username_cannot_be_empty"),trigger:"blur"}],password:[{required:!0,message:T("tool.password_cannot_be_empty"),trigger:"blur"},{min:8,max:20,message:T("tool.pwdLength"),trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,message:T("tool.password_includes_uppercase_lowercase_numbers_and_symbols"),trigger:"blur"}],emailCode:[{required:!0,message:T("tool.verification_code_cannot_be_empty"),trigger:"blur"},{min:6,max:6,message:T("tool.verification_code_must_be_6_digits"),trigger:"blur"}],email:[{required:!0,message:T("tool.email_address_cannot_be_empty"),trigger:"blur"},{type:"email",message:T("tool.please_enter_a_valid_email_address"),trigger:"blur"}]}),re=a(!1),ue=a(60),de=a(T("tool.send_verification_code")),me=t.get("userInfo")?null==(w=JSON.parse(t.get("userInfo")))?void 0:w.userId:"",pe=e=>{v(e).then((e=>{window.location.href=e}))},ge=()=>{if(!ie.value.email)return void _.error(T("tool.email_does_not_exist"));let e={email:ie.value.email,type:"RegisterCode"};f(e).then((e=>{e&&(()=>{re.value=!0,de.value=`${ue.value}${T("tool.retry_after_seconds")}`;let e=setInterval((()=>{ue.value-=1,de.value=`${ue.value}${T("tool.retry_after_seconds")}`,ue.value<=0&&(clearInterval(e),ue.value=60,de.value=T("tool.send_verification_code"),re.value=!1)}),1e3)})()}))};return i((()=>{me?le.push("/"):oe&&ae&&se&&n(oe,ae,se).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?t.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?t.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):t.set("userInfo",JSON.stringify(e.userInfo),{expires:365}),le.push("/"))}))})),(e,l)=>{const o=I,a=h,s=B,t=k,i=y;return c(),r("div",S,[u("div",U,[u("div",j,[u("div",C,[u("div",V,[u("div",null,[u("h1",N,d(e.$t("tool.create_account")),1),u("p",A,d(e.$t("tool.registration_greeting")),1)])]),u("div",q,[u("div",z,[u("div",R,[u("button",L,[u("span",O,[P,u("span",{class:"cl-socialButtonsBlockButtonText",onClick:l[0]||(l[0]=e=>pe(35))},d(e.$t("tool.continue_with_google")),1)])])]),u("div",G,[u("button",J,[u("span",E,[F,u("span",{class:"cl-socialButtonsBlockButtonText",onClick:l[1]||(l[1]=e=>pe(36))},d(e.$t("tool.continue_with_facebook")),1)])])])]),H,u("div",W,[m(t,{ref_key:"ruleFormRef",ref:te,style:{"max-width":"600px"},model:ie.value,rules:ce,"label-width":"auto",class:"demo-ruleForm","label-position":"left",size:e.formSize,"status-icon":""},{default:p((()=>[m(a,{label:e.$t("tool.username"),prop:"userName"},{default:p((()=>[m(o,{modelValue:ie.value.userName,"onUpdate:modelValue":l[2]||(l[2]=e=>ie.value.userName=e)},null,8,["modelValue"])])),_:1},8,["label"]),m(a,{label:e.$t("tool.email"),prop:"email"},{default:p((()=>[m(o,{modelValue:ie.value.email,"onUpdate:modelValue":l[3]||(l[3]=e=>ie.value.email=e)},null,8,["modelValue"])])),_:1},8,["label"]),m(a,{label:e.$t("tool.verification_code"),prop:"emailCode"},{default:p((()=>[m(o,{modelValue:ie.value.emailCode,"onUpdate:modelValue":l[4]||(l[4]=e=>ie.value.emailCode=e)},{append:p((()=>[m(s,{onClick:ge,disabled:re.value,type:"primary"},{default:p((()=>[g(d(de.value),1)])),_:1},8,["disabled"])])),_:1},8,["modelValue"])])),_:1},8,["label"]),m(a,{label:e.$t("tool.password"),prop:"password",style:{"padding-bottom":"20px"}},{default:p((()=>[m(o,{modelValue:ie.value.password,"onUpdate:modelValue":l[5]||(l[5]=e=>ie.value.password=e),"show-password":"true"},null,8,["modelValue"])])),_:1},8,["label"]),m(a,null,{default:p((()=>[u("div",Z,[M,u("div",D,[m(s,{class:"cl-formButtonPrimary cl-button 🔒️ cl-internal-ttumny",onClick:l[6]||(l[6]=e=>(async e=>{e&&await e.validate(((e,l)=>{e&&b(ie.value).then((e=>{e&&(_({type:"success",message:"注册成功，即将跳转到登录页..."}),setTimeout((()=>{location.href=ne.value?"/apps/login":"login"}),1e3))}))}))})(te.value))},{default:p((()=>[u("span",Q,[g(d(e.$t("tool.continue")),1),K])])),_:1})])])])),_:1})])),_:1},8,["model","rules","size"])])])]),u("div",X,[u("div",Y,[u("span",ee,d(e.$t("tool.alreadyhaveanaccount")),1),m(i,{href:ne.value?"/apps/login":"/login",class:"cl-footerActionLink"},{default:p((()=>[g(d(e.$t("tool.signIn")),1)])),_:1},8,["href"])])])])])])}}},[["__scopeId","data-v-e392a1cf"]]);export{le as default};
