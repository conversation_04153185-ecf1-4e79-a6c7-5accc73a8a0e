var e;import{aY as r,y as t,Z as s,aZ as a,a_ as o,a4 as d}from"./index-9ae28250.js";window.$decode=r;const i=t.get("userInfo")?null==(e=JSON.parse(t.get("userInfo")))?void 0:e.userId:"",n=s.create({baseURL:"https://ai.medon.com.cn",timeout:12e4,headers:{"Content-Type":"application/json;charset=utf-8"}});n.interceptors.request.use((e=>{const r=a.get("deviceId")||null;return e.headers["Visitor-Code"]=r,e.headers["User-Id"]=i,"get"===e.method&&(e.params=e.data),!e.noLoading&&o.showLoading(),e}),(e=>Promise.reject(e))),n.interceptors.response.use((e=>{const{status:t,data:s}=e;if(200===t){o.hideLoading();if(0===s.code){if(s.data){let e=s.data.split("").reverse().join("");return r(e).data&&"error"!=r(e).data.type&&"请充值会员"!=r(e).data&&200==r(e).code?Promise.resolve(r(e)):(d.error("访问太火爆了！请稍后再试~"),Promise.reject("访问太火爆了！请稍后再试~"))}return d.error("访问太火爆了！请稍后再试~"),Promise.reject("访问太火爆了！请稍后再试~")}Promise.reject(s),d.error(s.msg)}else d.error("访问太火爆了！请稍后再试~"),Promise.reject("访问太火爆了！请稍后再试~")}),(e=>(o.hideLoading(),Promise.reject(e))));const c=n.request,p=(e,r)=>c({url:`/paper/search/${e}`,method:"post",data:r}),m=e=>c({url:"/paper/rewrite/sentence",method:"post",data:e}),u=(e,r)=>c({url:`/paper/${e}`,method:"post",data:r});export{p as a,u as m,m as s};
