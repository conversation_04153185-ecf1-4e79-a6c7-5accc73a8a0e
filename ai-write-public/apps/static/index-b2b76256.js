/* empty css                  */import{g as e,i as a}from"./index-ac600168.js";import{r as l,x as t,u as o,w as i,d as n,e as s,j as r,t as u,g as d,h as c,m as v,F as p,y as m,f,z as g,k as h,i as y,A as b,B as w,C as x,D as _,G as k,E as I,H as S,I as T,J as C,K as $,L as z,M as B,N,O as q,P as j,o as U,Q as A,R as V,S as O,T as R,q as E,U as M,V as L,W as H,X as D,a as P,b as W,Y as J,Z as F,$ as X,a0 as Z,a1 as Y,a2 as G,c as K,a3 as Q,p as ee,a4 as ae,a5 as le,a6 as te,a7 as oe,a8 as ie,a9 as ne,aa as se,ab as re,v as ue}from"./index-bbabff90.js";/* empty css                  *//* empty css                  *//* empty css                 */import{c as de,r as ce,g as ve,s as pe,i as me,o as fe,a as ge,n as he,m as ye,b as be,u as we,d as xe,e as _e,f as ke,h as Ie,j as Se,k as Te,w as Ce,l as $e,p as ze,t as Be,q as Ne,v as qe,x as je,y as Ue,z as Ae,A as Ve,B as Oe,C as Re,D as Ee,E as Me,F as Le,G as He,H as De,I as Pe,J as We,K as Je,L as Fe,M as Xe,N as Ze,O as Ye,P as Ge,Q as Ke}from"./index-de1a6a96.js";import{r as Qe,a as ea,f as aa}from"./use-route-02c81a6c.js";/* empty css                   */const la={class:"p-3 flex-1 rounded-md"},ta={class:"text-[14px] font-bold mb-2 text-gray-600"},oa={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(e,{expose:a,emit:T}){const C=l(t.get("userInfo")?JSON.parse(t.get("userInfo")):{}),$=o(),z=l([]),B=l({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),N=l(""),q=e,j=q.type,U=q.fileVerify,A=q.label,V=q.required,O=q.max_length,R=q.options;"file"==j&&(N.value=null),"file-list"==j&&(N.value=[]);const E={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},M=()=>{let e="";return U.forEach(((a,l)=>{l<U.length-1?e+=E[a].join(",")+",":e+=E[a].join(",")})),e},L=T,H=(e,a,l)=>{},D=()=>{N.value=""},P=async e=>{const{file:a,onSuccess:l,onError:t}=e,o=new FormData;o.append("file",a),o.append("appId",$.params.uuid),o.append("user",C.value.userName);try{const e=await b(o);"file-list"==j?N.value.push({type:B.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id}):N.value={type:B.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},l(e,a)}catch(i){t(i)}return!1};R&&R.length>0&&(N.value=R[0]);return a({updateMessage:()=>{R&&R.length>0?N.value=R[0]:"file"==j?(N.value=null,z.value=[]):"file-list"==j?(N.value=[],z.value=[]):N.value=""}}),i(N,(e=>{L("update:value",e)}),{immediate:!0,deep:!0}),(e,a)=>{const l=w,t=x,o=_,i=k,b=I,T=S;return n(),s("div",la,[r("div",ta,u(d(A)),1),"paragraph"===d(j)||"text-input"===d(j)?(n(),c(l,{key:0,modelValue:N.value,"onUpdate:modelValue":a[0]||(a[0]=e=>N.value=e),type:"paragraph"===d(j)?"textarea":"text",rows:5,required:d(V),placeholder:`${d(A)}`,"show-word-limit":"",resize:"none",maxlength:d(O)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===d(j)?(n(),c(l,{key:1,modelValue:N.value,"onUpdate:modelValue":a[1]||(a[1]=e=>N.value=e),modelModifiers:{number:!0},type:"number",required:d(V),placeholder:`${d(A)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===d(j)?(n(),c(o,{key:2,modelValue:N.value,"onUpdate:modelValue":a[2]||(a[2]=e=>N.value=e),required:d(V),placeholder:`${d(A)}`},{default:v((()=>[(n(!0),s(p,null,m(d(R),(e=>(n(),c(t,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===d(j)||"file-list"===d(j)?(n(),c(T,{key:3,"file-list":z.value,"onUpdate:fileList":a[3]||(a[3]=e=>z.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":D,"before-remove":e.beforeRemove,limit:d(O),accept:M(),"auto-upload":!0,"on-Success":H,"http-request":P,"on-exceed":e.handleExceed},{default:v((()=>[f(b,{disabled:"file"===d(j)?1==z.value.length:z.value.length==d(O)},{default:v((()=>[f(i,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:v((()=>[f(d(g))])),_:1}),a[4]||(a[4]=h("从本地上传"))])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","limit","accept","on-exceed"])):y("",!0)])}}};let ia=0;function na(){const e=T(),{name:a="unknown"}=(null==e?void 0:e.type)||{};return`${a}-${++ia}`}function sa(e,a){if(!me||!window.IntersectionObserver)return;const l=new IntersectionObserver((e=>{a(e[0].intersectionRatio>0)}),{root:document.body}),t=()=>{e.value&&l.unobserve(e.value)};$(t),z(t),fe((()=>{e.value&&l.observe(e.value)}))}const[ra,ua]=ge("sticky");const da=ze(B({name:ra,props:{zIndex:he,position:ye("top"),container:Object,offsetTop:be(0),offsetBottom:be(0)},emits:["scroll","change"],setup(e,{emit:a,slots:t}){const o=l(),n=we(o),s=N({fixed:!1,width:0,height:0,transform:0}),r=l(!1),u=q((()=>xe("top"===e.position?e.offsetTop:e.offsetBottom))),d=q((()=>{if(r.value)return;const{fixed:e,height:a,width:l}=s;return e?{width:`${l}px`,height:`${a}px`}:void 0})),c=q((()=>{if(!s.fixed||r.value)return;const a=_e(ke(e.zIndex),{width:`${s.width}px`,height:`${s.height}px`,[e.position]:`${u.value}px`});return s.transform&&(a.transform=`translate3d(0, ${s.transform}px, 0)`),a})),v=()=>{if(!o.value||Se(o))return;const{container:l,position:t}=e,i=Te(o),n=ve(window);if(s.width=i.width,s.height=i.height,"top"===t)if(l){const e=Te(l),a=e.bottom-u.value-s.height;s.fixed=u.value>i.top&&e.bottom>0,s.transform=a<0?a:0}else s.fixed=u.value>i.top;else{const{clientHeight:e}=document.documentElement;if(l){const a=Te(l),t=e-a.top-u.value-s.height;s.fixed=e-u.value<i.bottom&&e>a.top,s.transform=t<0?-t:0}else s.fixed=e-u.value<i.bottom}(e=>{a("scroll",{scrollTop:e,isFixed:s.fixed})})(n)};return i((()=>s.fixed),(e=>a("change",e))),Ie("scroll",v,{target:n,passive:!0}),sa(o,v),i([Ce,$e],(()=>{o.value&&!Se(o)&&s.fixed&&(r.value=!0,j((()=>{const e=Te(o);s.width=e.width,s.height=e.height,r.value=!1})))})),()=>{var e;return f("div",{ref:o,style:d.value},[f("div",{class:ua({fixed:s.fixed&&!r.value}),style:c.value},[null==(e=t.default)?void 0:e.call(t)])])}}})),[ca,va]=ge("swipe"),pa={loop:Be,width:he,height:he,vertical:Boolean,autoplay:be(0),duration:be(500),touchable:Be,lazyRender:Boolean,initialSwipe:be(0),indicatorColor:String,showIndicators:Be,stopPropagation:Be},ma=Symbol(ca);const fa=ze(B({name:ca,props:pa,emits:["change","dragStart","dragEnd"],setup(e,{emit:a,slots:t}){const o=l(),n=l(),s=N({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let r=!1;const u=Ne(),{children:d,linkChildren:c}=qe(ma),v=q((()=>d.length)),p=q((()=>s[e.vertical?"height":"width"])),m=q((()=>e.vertical?u.deltaY.value:u.deltaX.value)),g=q((()=>{if(s.rect){return(e.vertical?s.rect.height:s.rect.width)-p.value*v.value}return 0})),h=q((()=>p.value?Math.ceil(Math.abs(g.value)/p.value):v.value)),y=q((()=>v.value*p.value)),b=q((()=>(s.active+v.value)%v.value)),w=q((()=>{const a=e.vertical?"vertical":"horizontal";return u.direction.value===a})),x=q((()=>{const a={transitionDuration:`${s.swiping?0:e.duration}ms`,transform:`translate${e.vertical?"Y":"X"}(${+s.offset.toFixed(2)}px)`};if(p.value){const l=e.vertical?"height":"width",t=e.vertical?"width":"height";a[l]=`${y.value}px`,a[t]=e[t]?`${e[t]}px`:""}return a})),_=(a,l=0)=>{let t=a*p.value;e.loop||(t=Math.min(t,-g.value));let o=l-t;return e.loop||(o=Re(o,g.value,0)),o},k=({pace:l=0,offset:t=0,emitChange:o})=>{if(v.value<=1)return;const{active:i}=s,n=(a=>{const{active:l}=s;return a?e.loop?Re(l+a,-1,v.value):Re(l+a,0,h.value):l})(l),r=_(n,t);if(e.loop){if(d[0]&&r!==g.value){const e=r<g.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==r){const e=r>0;d[v.value-1].setOffset(e?-y.value:0)}}s.active=n,s.offset=r,o&&n!==i&&a("change",b.value)},I=()=>{s.swiping=!0,s.active<=-1?k({pace:v.value}):s.active>=v.value&&k({pace:-v.value})},S=()=>{I(),u.reset(),Ve((()=>{s.swiping=!1,k({pace:1,emitChange:!0})}))};let T;const C=()=>clearTimeout(T),B=()=>{C(),+e.autoplay>0&&v.value>1&&(T=setTimeout((()=>{S(),B()}),+e.autoplay))},V=(a=+e.initialSwipe)=>{if(!o.value)return;const l=()=>{var l,t;if(!Se(o)){const a={width:o.value.offsetWidth,height:o.value.offsetHeight};s.rect=a,s.width=+(null!=(l=e.width)?l:a.width),s.height=+(null!=(t=e.height)?t:a.height)}v.value&&-1===(a=Math.min(v.value-1,a))&&(a=v.value-1),s.active=a,s.swiping=!0,s.offset=_(a),d.forEach((e=>{e.setOffset(0)})),B()};Se(o)?j().then(l):l()},O=()=>V(s.active);let R;const E=a=>{!e.touchable||a.touches.length>1||(u.start(a),r=!1,R=Date.now(),C(),I())},M=()=>{if(!e.touchable||!s.swiping)return;const l=Date.now()-R,t=m.value/l;if((Math.abs(t)>.25||Math.abs(m.value)>p.value/2)&&w.value){const a=e.vertical?u.offsetY.value:u.offsetX.value;let l=0;l=e.loop?a>0?m.value>0?-1:1:0:-Math[m.value>0?"ceil":"floor"](m.value/p.value),k({pace:l,emitChange:!0})}else m.value&&k({pace:0});r=!1,s.swiping=!1,a("dragEnd",{index:b.value}),B()},L=(a,l)=>{const t=l===b.value,o=t?{backgroundColor:e.indicatorColor}:void 0;return f("i",{style:o,class:va("indicator",{active:t})},null)};return je({prev:()=>{I(),u.reset(),Ve((()=>{s.swiping=!1,k({pace:-1,emitChange:!0})}))},next:S,state:s,resize:O,swipeTo:(a,l={})=>{I(),u.reset(),Ve((()=>{let t;t=e.loop&&a===v.value?0===s.active?0:a:a%v.value,l.immediate?Ve((()=>{s.swiping=!1})):s.swiping=!1,k({pace:t-s.active,emitChange:!0})}))}}),c({size:p,props:e,count:v,activeIndicator:b}),i((()=>e.initialSwipe),(e=>V(+e))),i(v,(()=>V(s.active))),i((()=>e.autoplay),B),i([Ce,$e,()=>e.width,()=>e.height],O),i(Ue(),(e=>{"visible"===e?B():C()})),U(V),A((()=>V(s.active))),Ae((()=>V(s.active))),$(C),z(C),Ie("touchmove",(l=>{if(e.touchable&&s.swiping&&(u.move(l),w.value)){!e.loop&&(0===s.active&&m.value>0||s.active===v.value-1&&m.value<0)||(Oe(l,e.stopPropagation),k({offset:m.value}),r||(a("dragStart",{index:b.value}),r=!0))}}),{target:n}),()=>{var a;return f("div",{ref:o,class:va()},[f("div",{ref:n,style:x.value,class:va("track",{vertical:e.vertical}),onTouchstartPassive:E,onTouchend:M,onTouchcancel:M},[null==(a=t.default)?void 0:a.call(t)]),t.indicator?t.indicator({active:b.value,total:v.value}):e.showIndicators&&v.value>1?f("div",{class:va("indicators",{vertical:e.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[ga,ha]=ge("tabs");var ya=B({name:ga,props:{count:Ee(Number),inited:Boolean,animated:Boolean,duration:Ee(he),swipeable:Boolean,lazyRender:Boolean,currentIndex:Ee(Number)},emits:["change"],setup(e,{emit:a,slots:t}){const o=l(),n=e=>a("change",e),s=()=>{var a;const l=null==(a=t.default)?void 0:a.call(t);return e.animated||e.swipeable?f(fa,{ref:o,loop:!1,class:ha("track"),duration:1e3*+e.duration,touchable:e.swipeable,lazyRender:e.lazyRender,showIndicators:!1,onChange:n},{default:()=>[l]}):l},r=a=>{const l=o.value;l&&l.state.active!==a&&l.swipeTo(a,{immediate:!e.inited})};return i((()=>e.currentIndex),r),U((()=>{r(e.currentIndex)})),je({swipeRef:o}),()=>f("div",{class:ha("content",{animated:e.animated||e.swipeable})},[s()])}});const[ba,wa]=ge("tabs"),xa={type:ye("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:be(0),duration:be(.3),animated:Boolean,ellipsis:Be,swipeable:Boolean,scrollspy:Boolean,offsetTop:be(0),background:String,lazyRender:Be,showHeader:Be,lineWidth:he,lineHeight:he,beforeChange:Function,swipeThreshold:be(5),titleActiveColor:String,titleInactiveColor:String},_a=Symbol(ba);var ka=B({name:ba,props:xa,emits:["change","scroll","rendered","clickTab","update:active"],setup(e,{emit:a,slots:t}){let o,n,s,r,u;const d=l(),c=l(),v=l(),p=l(),m=na(),g=we(d),[h,y]=function(){const e=l([]),a=[];return C((()=>{e.value=[]})),[e,l=>(a[l]||(a[l]=a=>{e.value[l]=a}),a[l])]}(),{children:b,linkChildren:w}=qe(_a),x=N({inited:!1,position:"",lineStyle:{},currentIndex:-1}),_=q((()=>b.length>+e.swipeThreshold||!e.ellipsis||e.shrink)),k=q((()=>({borderColor:e.color,background:e.background}))),I=(e,a)=>{var l;return null!=(l=e.name)?l:a},S=q((()=>{const e=b[x.currentIndex];if(e)return I(e,x.currentIndex)})),T=q((()=>xe(e.offsetTop))),$=q((()=>e.sticky?T.value+o:0)),z=a=>{const l=c.value,t=h.value;if(!(_.value&&l&&t&&t[x.currentIndex]))return;const o=t[x.currentIndex].$el,i=o.offsetLeft-(l.offsetWidth-o.offsetWidth)/2;r&&r(),r=function(e,a,l){let t,o=0;const i=e.scrollLeft,n=0===l?1:Math.round(1e3*l/16);let s=i;return function l(){s+=(a-i)/n,e.scrollLeft=s,++o<n&&(t=ce(l))}(),function(){de(t)}}(l,i,a?0:+e.duration)},B=()=>{const a=x.inited;j((()=>{const l=h.value;if(!l||!l[x.currentIndex]||"line"!==e.type||Se(d.value))return;const t=l[x.currentIndex].$el,{lineWidth:o,lineHeight:i}=e,n=t.offsetLeft+t.offsetWidth/2,s={width:Me(o),backgroundColor:e.color,transform:`translateX(${n}px) translateX(-50%)`};if(a&&(s.transitionDuration=`${e.duration}s`),Le(i)){const e=Me(i);s.height=e,s.borderRadius=e}x.lineStyle=s}))},U=(l,t)=>{const o=(e=>{const a=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=a}})(l);if(!Le(o))return;const i=b[o],n=I(i,o),r=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,t||z(),B()),n!==e.active&&(a("update:active",n),r&&a("change",n,i.title)),s&&!e.scrollspy&&De(Math.ceil(Pe(d.value)-T.value))},V=(e,a)=>{const l=b.find(((a,l)=>I(a,l)===e)),t=l?b.indexOf(l):0;U(t,a)},O=(a=!1)=>{if(e.scrollspy){const l=b[x.currentIndex].$el;if(l&&g.value){const t=Pe(l,g.value)-$.value;n=!0,u&&u(),u=function(e,a,l,t){let o,i=ve(e);const n=i<a,s=0===l?1:Math.round(1e3*l/16),r=(a-i)/s;return function l(){i+=r,(n&&i>a||!n&&i<a)&&(i=a),pe(e,i),n&&i<a||!n&&i>a?o=ce(l):t&&(o=ce(t))}(),function(){de(o)}}(g.value,t,a?0:+e.duration,(()=>{n=!1}))}}},R=(l,t,o)=>{const{title:i,disabled:n}=b[t],s=I(b[t],t);n||(We(e.beforeChange,{args:[s],done:()=>{U(t),O()}}),Qe(l)),a("clickTab",{name:s,title:i,event:o,disabled:n})},E=e=>{s=e.isFixed,a("scroll",e)},M=()=>{if("line"===e.type&&b.length)return f("div",{class:wa("line"),style:x.lineStyle},null)},L=()=>{var a,l,o;const{type:i,border:n,sticky:s}=e,r=[f("div",{ref:s?void 0:v,class:[wa("wrap"),{[He]:"line"===i&&n}]},[f("div",{ref:c,role:"tablist",class:wa("nav",[i,{shrink:e.shrink,complete:_.value}]),style:k.value,"aria-orientation":"horizontal"},[null==(a=t["nav-left"])?void 0:a.call(t),b.map((e=>e.renderTitle(R))),M(),null==(l=t["nav-right"])?void 0:l.call(t)])]),null==(o=t["nav-bottom"])?void 0:o.call(t)];return s?f("div",{ref:v},[r]):r},H=()=>{B(),j((()=>{var e,a;z(!0),null==(a=null==(e=p.value)?void 0:e.swipeRef.value)||a.resize()}))};i((()=>[e.color,e.duration,e.lineWidth,e.lineHeight]),B),i(Ce,H),i((()=>e.active),(e=>{e!==S.value&&V(e)})),i((()=>b.length),(()=>{x.inited&&(V(e.active),B(),j((()=>{z(!0)})))}));return je({resize:H,scrollTo:e=>{j((()=>{V(e),O(!0)}))}}),A(B),Ae(B),fe((()=>{V(e.active,!0),j((()=>{x.inited=!0,v.value&&(o=Te(v.value).height),z(!0)}))})),sa(d,B),Ie("scroll",(()=>{if(e.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:a}=Te(b[e].$el);if(a>$.value)return 0===e?0:e-1}return b.length-1})();U(e)}}),{target:g,passive:!0}),w({id:m,props:e,setLine:B,scrollable:_,onRendered:(e,l)=>a("rendered",e,l),currentName:S,setTitleRefs:y,scrollIntoView:z}),()=>f("div",{ref:d,class:wa([e.type])},[e.showHeader?e.sticky?f(da,{container:d.value,offsetTop:T.value,onScroll:E},{default:()=>[L()]}):L():null,f(ya,{ref:p,count:b.length,inited:x.inited,animated:e.animated,duration:e.duration,swipeable:e.swipeable,lazyRender:e.lazyRender,currentIndex:x.currentIndex,onChange:U},{default:()=>{var e;return[null==(e=t.default)?void 0:e.call(t)]}})])}});const Ia=Symbol(),[Sa,Ta]=ge("tab"),Ca=B({name:Sa,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:he,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:Be},setup(e,{slots:a}){const l=q((()=>{const a={},{type:l,color:t,disabled:o,isActive:i,activeColor:n,inactiveColor:s}=e;t&&"card"===l&&(a.borderColor=t,o||(i?a.backgroundColor=t:a.color=t));const r=i?n:s;return r&&(a.color=r),a})),t=()=>{const l=f("span",{class:Ta("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||Le(e.badge)&&""!==e.badge?f(Je,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[l]}):l};return()=>f("div",{id:e.id,role:"tab",class:[Ta([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:l.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[t()])}}),[$a,za]=ge("swipe-item");const Ba=ze(B({name:$a,setup(e,{slots:a}){let l;const t=N({offset:0,inited:!1,mounted:!1}),{parent:o,index:i}=Fe(ma);if(!o)return;const n=q((()=>{const e={},{vertical:a}=o.props;return o.size.value&&(e[a?"height":"width"]=`${o.size.value}px`),t.offset&&(e.transform=`translate${a?"Y":"X"}(${t.offset}px)`),e})),s=q((()=>{const{loop:e,lazyRender:a}=o.props;if(!a||l)return!0;if(!t.mounted)return!1;const n=o.activeIndicator.value,s=o.count.value-1,r=0===n&&e?s:n-1,u=n===s&&e?0:n+1;return l=i.value===n||i.value===r||i.value===u,l}));return U((()=>{j((()=>{t.mounted=!0}))})),je({setOffset:e=>{t.offset=e}}),()=>{var e;return f("div",{class:za(),style:n.value},[s.value?null==(e=a.default)?void 0:e.call(a):null])}}})),[Na,qa]=ge("tab");const ja=ze(B({name:Na,props:_e({},ea,{dot:Boolean,name:he,badge:he,title:String,disabled:Boolean,titleClass:Xe,titleStyle:[String,Object],showZeroBadge:Be}),setup(e,{slots:a}){const t=na(),o=l(!1),n=T(),{parent:s,index:r}=Fe(_a);if(!s)return;const u=()=>{var a;return null!=(a=e.name)?a:r.value},d=q((()=>{const a=u()===s.currentName.value;return a&&!o.value&&(o.value=!0,s.props.lazyRender&&j((()=>{s.onRendered(u(),e.title)}))),a})),c=l(""),v=l("");V((()=>{const{titleClass:a,titleStyle:l}=e;c.value=a?O(a):"",v.value=l&&"string"!=typeof l?R(E(l)):l}));const p=l(!d.value);return i(d,(e=>{e?p.value=!1:Ve((()=>{p.value=!0}))})),i((()=>e.title),(()=>{s.setLine(),s.scrollIntoView()})),M(Ia,d),je({id:t,renderTitle:l=>f(Ca,D({key:t,id:`${s.id}-${r.value}`,ref:s.setTitleRefs(r.value),style:v.value,class:c.value,isActive:d.value,controls:t,scrollable:s.scrollable.value,activeColor:s.props.titleActiveColor,inactiveColor:s.props.titleInactiveColor,onClick:e=>l(n.proxy,r.value,e)},Ze(s.props,["type","color","shrink"]),Ze(e,["dot","badge","title","disabled","showZeroBadge"])),{title:a.title})}),()=>{var e;const l=`${s.id}-${r.value}`,{animated:i,swipeable:n,scrollspy:u,lazyRender:c}=s.props;if(!a.default&&!i)return;const v=u||d.value;if(i||n)return f(Ba,{id:t,role:"tabpanel",class:qa("panel-wrapper",{inactive:p.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":l,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[f("div",{class:qa("panel")},[null==(e=a.default)?void 0:e.call(a)])]}});const m=o.value||u||!c?null==(e=a.default)?void 0:e.call(a):null;return L(f("div",{id:t,role:"tabpanel",class:qa("panel"),tabindex:v?0:-1,"aria-labelledby":l,"data-allow-mismatch":"attribute"},[m]),[[H,v]])}}})),Ua=ze(ka),Aa={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},Va={class:"pc_container",style:{display:"flex"}},Oa={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%"}},Ra={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Ea={class:"pc_right bg-[#fff]"},Ma={id:"typing-area"},La={key:0,class:"decContaniner nop bg-[#fff]"},Ha={key:0,class:"img_box"},Da=["src"],Pa={key:1,class:"icon"},Wa={class:"process_text label_width"},Ja={key:0,class:"process"},Fa={key:0,class:"img_box"},Xa=["src"],Za={key:1,class:"icon"},Ya={class:"process"},Ga={class:"process_text"},Ka={key:2},Qa=["src"],el=["src"],al={class:"mobile_container"},ll={class:"p-3",style:{display:"flex","justify-content":"space-between"}},tl={class:"mobile_right"},ol={id:"typing-area"},il={key:0,class:"decContaniner nop bg-[#fff]"},nl={key:0,class:"img_box"},sl=["src"],rl={key:1,class:"icon"},ul={class:"process_text label_width"},dl={key:0,class:"img_box"},cl=["src"],vl={key:1,class:"icon"},pl={class:"process"},ml={class:"process_text"},fl={key:2},gl=P({__name:"index",props:{currentItem:{type:Object,default:()=>{}}},setup(g){const b=g,w=e("loading.png"),x=e("copy.png"),_=N({}),S=o(),T={},C=l([]),$=l(t.get("userInfo")?JSON.parse(t.get("userInfo")):{}),z=l(null),{t:B,locale:j}=W(),A=J(),V=l(!1);let R=l("a"),E=l(!0),M=l("");const L=l(""),H=l(null),D=l(null),P=l(["1","2"]),de=l(!1),ce=l(!1),ve=l(!1);let pe;const me=async()=>{var e;await ae({appId:S.params.uuid,user:$.value.userName,mode:null==(e=z.value)?void 0:e.mode,task_id:L.value}),setTimeout((()=>{Me.abort(),Le=!0,ze.value=[],de.value=!1,Be.value.length&&Be.value.forEach((e=>{e.status=!0})),Fe()}),0)},fe=()=>{ve.value=!1},ge=async(e,a)=>{var l;let t=X();if(null==(l=$.value)?void 0:l.userId){const l={appUuid:a,priceId:e.priceId,monthNum:e.monthNum};let t=await le(l);t&&(te({type:"success",message:B("tool.sS")}),setTimeout((()=>{location.href=t}),1e3))}else t&&"zh-CN"!=t?A.push((isUp,"/login")):window.addLoginDom()};U((async()=>{var e,l;const o=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${o}px`);if(["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=S.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(l=S.params)?void 0:l.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),j.value=F(),!t.get("userInfo")){localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=X();return void(e&&"zh-CN"!=e?A.push("/login"):window.addLoginDom())}await ta(),Ce();const{customCss:i,customJs:n}=await Z(S.params.appUuid);a(i,n)}));const he=e=>{R.value=e.name},ye=async()=>{ve.value=!0},be=()=>{S.params.uuid&&oe({appId:S.params.uuid,user:$.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(C.value=e.user_input_form,e.user_input_form.forEach((e=>{const a=Object.keys(e)[0],l=e[a].variable;T[l]={label:e[a].label},_[l]=""})))}))},we=q((()=>!!C.value.length)),xe=q((()=>C.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),_e=()=>{S.params.uuid&&ie({appId:S.params.uuid,user:$.value.userName}).then((e=>{z.value={...e}}))},ke=l(!1),Ie=l(!1),Se=l(!1),Te=l(!1),Ce=async()=>{var e,a;let l=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=$.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:S.params.uuid,userUuid:null==(a=$.value)?void 0:a.openid}];await Y.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",l)},$e=()=>{var e,a;if(0!=xe.value.length||(l=_,Object.values(l).some((e=>e)))){var l;for(let e in _)if(xe.value.includes(e)&&!_[e])return void te({message:`${T[e].label}${B("tool.requiredfield")}`,type:"error"});(null==(e=z.value)?void 0:e.mode)&&(["advanced-chat","chat"].includes(null==(a=z.value)?void 0:a.mode)?te({type:"success",message:B("tool.planning")}):"completion"==z.value.mode?(E.value=!1,setTimeout((()=>{R.value="b"}),1e3),Je()):(E.value=!1,setTimeout((()=>{R.value="b"}),1e3),We()))}else te({message:`${B("tool.enterquestion")}`,type:"error"})},ze=l([]);var Be=l([]),Ne=l([]);const qe=l(""),je=l(0),Ue=l(""),Ae=l(!1),Ve=l(!1);let Oe=l(0);const Re=l(!1),Ee=l(!1);let Me,Le=!1,He=!1;i(Be,(()=>{De()}),{deep:!0});const De=()=>{Oe.value<Be.value.length&&(Ne.value.push(Be.value[Oe.value]),Oe.value++,setTimeout(De,1e3))},Pe=()=>{je.value<qe.value.length?(Ae.value=!0,Ue.value+=qe.value.charAt(je.value),je.value++,setTimeout(Pe,20)):(Ee.value=!1,Ae.value=!1,Se.value=!0,Fe())},We=async()=>{ce.value=!0,de.value=!0,M.value="",Be.value=[],Ne.value=[],Oe.value=0,Ue.value="",qe.value="",ze.value=[],Re.value=!1,Le=!1,je.value=0,Me=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run?locale=zh`;0,ke.value=!0,Ie.value=!0,Te.value=!1,await aa(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:S.params.uuid,user:$.value.userName,inputs:{..._,outputLanguage:_.outputLanguage?_.outputLanguage:"中文"==ne()?"简体中文":ne()},requestId:crypto.randomUUID(),files:[],response_mode:"streaming",appUuid:S.params.appUuid}),onmessage(e){var a,l,t,o,i,n,s,r,u,d;if(e.data.trim())try{const c=JSON.parse(e.data);if(L.value=c.task_id,c.error)throw new Error(c.error);"智能体推理思维链"==(null==(a=null==c?void 0:c.data)?void 0:a.title)&&"node_finished"===c.event&&(Ee.value=!0,qe.value=null==(o=JSON.parse(null==(t=null==(l=null==c?void 0:c.data)?void 0:l.outputs)?void 0:t.text))?void 0:o.text,Pe()),"node_started"!==c.event||Re.value||"开始"==(null==(i=null==c?void 0:c.data)?void 0:i.title)||Be.value.push({node_id:null==(n=null==c?void 0:c.data)?void 0:n.node_id,title:null==(s=null==c?void 0:c.data)?void 0:s.title,status:!1}),"error"===c.event&&(5047==c.code&&(Ve.value=!0),Re.value=!0,de.value=!1,Le=!0,Ie.value=!1,M.value=null==c?void 0:c.message),"node_finished"===c.event&&Be.value.forEach((e=>{var a;e.node_id==(null==(a=null==c?void 0:c.data)?void 0:a.node_id)&&(e.status=!0)})),"text_chunk"===c.event&&(V.value=!0,ze.value.push(null==(r=null==c?void 0:c.data)?void 0:r.text),Ee.value||Fe()),"workflow_started"===c.event&&(ke.value=!1),"workflow_finished"===c.event&&(Le=!0,Re.value=!0,de.value=!1,Ie.value=!1,Se.value=!1,V.value||(ze.value.push(null==(d=null==(u=null==c?void 0:c.data)?void 0:u.outputs)?void 0:d.text),Ee.value||Fe()))}catch(c){Qe(c)}},onerror(e){Qe(e)},signal:Me.signal,openWhenHidden:!0})}catch(e){Qe()}},Je=async()=>{M.value="",ze.value=[],Ee.value=!1,Le=!1,Me=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages?locale=zh`;0,ke.value=!0,Ie.value=!0,Te.value=!1,await aa(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:S.params.uuid,user:$.value.userName,inputs:{..._,outputLanguage:ne()},files:[],response_mode:"streaming",appUuid:S.params.appUuid,requestId:crypto.randomUUID()}),onmessage(e){if(ke.value=!1,Se.value=!0,e.data.trim())try{const a=JSON.parse(e.data);if(L.value=a.task_id,a.error)throw new Error(a.error);"error"===a.event&&(5047==a.code&&(Ve.value=!0),Le=!0,M.value=null==a?void 0:a.message,Se.value=!1,Ie.value=!1),"message"===a.event&&(ze.value.push(null==a?void 0:a.answer),Ee.value||Fe()),"message_end"===a.event&&(Le=!0,Ie.value=!1,Se.value=!1)}catch(a){Qe(a)}},onerror(e){Qe(e)},signal:Me.signal,openWhenHidden:!0})}catch(e){Qe()}},Fe=()=>{if(0===ze.value.length)return Ee.value=!1,He=!0,void Xe();Ee.value=!0;const e=ze.value.shift();Ze(e).then((()=>{Fe()}))},Xe=()=>{He&&Le&&(Ie.value=!1,Se.value=!1,Te.value=!0)},Ze=e=>new Promise((a=>{let l=0;pe=setInterval((()=>{if(l<(null==e?void 0:e.length)){M.value+=e[l++];const a=document.getElementsByClassName("pc_right");a[0].scrollTop=a[0].scrollHeight;const t=document.getElementsByClassName("mobile_right");t[0].scrollTop=t[0].scrollHeight}else clearInterval(pe),a()}),0)})),Qe=()=>{setTimeout((()=>{Me.abort()}),0),de.value=!1,ke.value=!1,Se.value=!1,Ie.value=!1,Ee.value=!1,te.error(B("tool.accessbusy")),M.value=B("tool.accessbusy")},ea=async()=>{try{await navigator.clipboard.writeText(M.value),te({type:"success",message:B("tool.copysuccess")})}catch(e){te(e)}},la=()=>{for(let e in _)_[e]="";H.value.forEach((e=>{e.updateMessage()})),D.value.forEach((e=>{e.updateMessage()}))},ta=async()=>{if(localStorage.getItem("yudaoToken"))return be(),void _e();try{await G({userId:$.value.userId,userName:$.value.userName,realName:$.value.realName,avatar:$.value.avatar,plaintextUserId:$.value.plaintextUserId,mobile:$.value.mobile,email:$.value.email}).then((async e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),be(),_e())}))}catch(e){}};return(e,a)=>{const l=I,t=k,o=se,i=re,g=K("v-md-preview"),S=ue;return n(),s("div",Aa,[r("div",Va,[d(we)?(n(),s(p,{key:0},[r("div",Oa,[(n(!0),s(p,null,m(d(C),((a,l)=>(n(),s("div",{class:"flex",key:l},[(n(!0),s(p,null,m(a,((a,l)=>(n(),c(oa,{key:a.variable,type:l,label:e.$t(null==a?void 0:a.label),value:d(_)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>d(_)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRef",ref:H},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),r("div",Ra,[f(l,{onClick:la},{default:v((()=>[h(u(e.$t("tool.clear")),1)])),_:1}),f(l,{onClick:$e,loading:d(Ie),type:"primary"},{default:v((()=>[h(u(e.$t("tool.execute")),1)])),_:1},8,["loading"])])]),r("div",Ea,[r("div",Ma,[d(Ne).length>0||d(Ue)||d(ce)?(n(),s("div",La,[f(i,{modelValue:d(P),"onUpdate:modelValue":a[0]||(a[0]=e=>ee(P)?P.value=e:null)},{default:v((()=>[f(o,{name:"1"},{title:v((()=>[d(de)?(n(),s("div",Ha,[r("img",{src:d(w),alt:"loading"},null,8,Da)])):(n(),s("div",Pa,[f(t,null,{default:v((()=>[f(d(Q))])),_:1})])),h(" "+u(e.$t("tool.execution_progress")),1)])),default:v((()=>[(n(!0),s(p,null,m(d(Ne),((l,t)=>(n(),s("div",{key:t,class:"process"},[r("div",Wa,u(l.title),1),a[5]||(a[5]=h("    ")),r("span",{style:{color:"#36b15e"},class:O(l.status?"":"loading-text")},u(l.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),r("div",null,[d(Ue)?(n(),s("div",Ja)):y("",!0)]),d(Ue)?(n(),c(o,{key:0,name:"2"},{title:v((()=>[d(Ae)?(n(),s("div",Fa,[r("img",{src:d(w),alt:"loading"},null,8,Xa)])):(n(),s("div",Za,[f(t,null,{default:v((()=>[f(d(Q))])),_:1})])),h(" "+u(e.$t("tool.reasoning_process")),1)])),default:v((()=>[r("div",Ya,[r("div",Ga,u(d(Ue)),1)])])),_:1})):y("",!0)])),_:1},8,["modelValue"])])):y("",!0),d(M)&&!d(Ve)?(n(),c(g,{key:1,text:d(M),id:"previewMd"},null,8,["text"])):y("",!0),d(Ve)?(n(),s("div",Ka,[h(u(d(M))+" ",1),f(l,{type:"text",onClick:ye},{default:v((()=>[h(u(e.$t("market.subscribe")),1)])),_:1})])):y("",!0),r("div",null,[d(Se)?(n(),s("img",{key:0,src:d(w),alt:"loading",class:"spinner"},null,8,Qa)):y("",!0),d(Se)?(n(),s("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:me},u(e.$t("tool.stopGeneration")),1)):y("",!0),d(Te)?(n(),s("img",{key:2,onClick:ea,src:d(x),alt:"",style:{width:"20px"},class:"copy"},null,8,el)):y("",!0)])])])],64)):y("",!0)]),r("div",al,[f(d(Ua),{active:d(R),shrink:"","line-width":"20",onClickTab:he},{default:v((()=>[f(d(ja),{title:"输入",name:"a"},{default:v((()=>[(n(!0),s(p,null,m(d(C),((a,l)=>(n(),s("div",{class:"flex",key:l},[(n(!0),s(p,null,m(a,((a,l)=>(n(),c(oa,{key:a.variable,type:l,label:e.$t(null==a?void 0:a.label),value:d(_)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>d(_)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRefs",ref:D},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),r("div",ll,[f(l,{onClick:la},{default:v((()=>a[6]||(a[6]=[h("Clear")]))),_:1}),f(l,{onClick:a[1]||(a[1]=e=>$e()),loading:d(Ie),type:"primary"},{default:v((()=>a[7]||(a[7]=[h("Execute")]))),_:1},8,["loading"])])])),_:1}),f(d(ja),{title:"结果",name:"b",disabled:d(E)},{default:v((()=>[r("div",tl,[r("div",ol,[d(Be).length>0||d(Ue)?(n(),s("div",il,[f(i,{modelValue:d(P),"onUpdate:modelValue":a[2]||(a[2]=e=>ee(P)?P.value=e:null)},{default:v((()=>[f(o,{name:"1"},{title:v((()=>[d(de)?(n(),s("div",nl,[r("img",{src:d(w),alt:"loading"},null,8,sl)])):(n(),s("div",rl,[f(t,null,{default:v((()=>[f(d(Q))])),_:1})])),h(" "+u(e.$t("tool.execution_progress")),1)])),default:v((()=>[(n(!0),s(p,null,m(d(Be),((l,t)=>(n(),s("div",{key:t,class:"process"},[r("div",ul,u(l.title),1),a[8]||(a[8]=h("    ")),r("span",{style:{color:"#36b15e"},class:O(l.status?"":"loading-text")},u(l.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),a[9]||(a[9]=r("div",null,[r("div",{class:"process"})],-1)),d(Ue)?(n(),c(o,{key:0,title:"推导过程",name:"2"},{title:v((()=>[d(Ae)?(n(),s("div",dl,[r("img",{src:d(w),alt:"loading"},null,8,cl)])):(n(),s("div",vl,[f(t,null,{default:v((()=>[f(d(Q))])),_:1})])),h(" "+u(e.$t("tool.reasoning_process")),1)])),default:v((()=>[r("div",pl,[r("div",ml,u(d(Ue)),1)])])),_:1})):y("",!0)])),_:1},8,["modelValue"])])):y("",!0),d(M)&&!d(Ve)?(n(),c(g,{key:1,text:d(M),id:"previewMd"},null,8,["text"])):y("",!0),d(Ve)?(n(),s("div",fl,[h(u(d(M))+" ",1),f(l,{type:"text",onClick:ye},{default:v((()=>[h(u(e.$t("market.subscribe")),1)])),_:1})])):y("",!0)])])])),_:1},8,["disabled"])])),_:1},8,["active"])]),d(ve)?(n(),c(S,{key:0,modelValue:d(ve),"onUpdate:modelValue":a[3]||(a[3]=e=>ee(ve)?ve.value=e:null),class:"payPC","show-close":!1},{default:v((()=>[f(Ye,{userInfo:d($),appTypes:e.appTypes,currentItem:b.currentItem,onToAgreement:e.toAgreement,onClose:fe,onSubscribe:ge},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["modelValue"])):y("",!0),d(ve)?(n(),c(d(Ke),{key:1,show:d(ve),"onUpdate:show":a[4]||(a[4]=e=>ee(ve)?ve.value=e:null),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"90%"}},{default:v((()=>[f(Ge,{userInfo:d($),appTypes:e.appTypes,currentItem:b.currentItem,onToAgreement:e.toAgreement,onClose:fe},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["show"])):y("",!0)])}}},[["__scopeId","data-v-5eb46fe6"]]);export{gl as default};
