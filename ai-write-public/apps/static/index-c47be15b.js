/* empty css                  *//* empty css                   *//* empty css                *//* empty css                 */import{a as e,r as t,aM as n,d as r,e as a,j as s,f as i,g as l,q as o,i as c,F as h,z as u,a0 as g,t as f,T as p,h as d,m as b,aN as v,aO as m,a4 as x,aP as y,C as w,H as _,aQ as A,ag as k,ah as M}from"./index-b8b250c3.js";import{m as C}from"./index-54156e1e.js";/* empty css                   */var E=-1,j=1,S=0;function T(e){return"<div>"+function(e){var t=-1;return e.replace(/<(ins|del|span)>/g,(function(e){return t++,"<".concat(e.slice(1,-1),' key="').concat(t,'">')}))}(function(e){for(var t=[],n=/&/g,r=/</g,a=/>/g,s=/\n/g,i=0;i<e.length;i++){var l=e[i][0],o=e[i][1].replace(n,"&amp;").replace(r,"&lt;").replace(a,"&gt;").replace(s,"<br/>");switch(l){case j:t[i]="<ins style='background-color:rgba(0,145,255,0);color:#ff0101;text-decoration: underline;' >"+o+"</ins>";break;case E:t[i]="<del style='background: rgba(0,145,255,0.2);color:#0091FF;' >"+o+"</del>";break;case S:t[i]="<span>"+o+"</span>"}}return t.join("")}(e)).replace(/\n/g,"<br>")+"</div>"}function R(e,t){var n,r;n=t,["Here's the corrected ","Here's a corrected ","Here's the proofread ","Here's a proofread","Here is a proofread","Here's a possible revision"].forEach((function(e){if(n.toLowerCase().startsWith(e.toLowerCase())){var t=n.indexOf(":",e.length);n=n.substring(t+1)}}));var a=function(e,t){var n,r=[],a=new(function(){function e(e){e=e||{},this.Timeout=e.timeout||1,this.EditCost=e.editCost||4}return e.prototype.main=function(e,t,n,r){void 0===r&&(r=this.Timeout<=0?Number.MAX_VALUE:(new Date).getTime()+1e3*this.Timeout);var a=r;if(null==e||null==t)throw new Error("Null input. (diff_main)");if(e==t)return e?[[S,e]]:[];void 0===n&&(n=!0);var s=n,i=this.commonPrefix(e,t),l=e.substring(0,i);this.l=l,e=e.substring(i),t=t.substring(i),i=this.commonSuffix(e,t);var o=e.substring(e.length-i);e=e.substring(0,e.length-i),t=t.substring(0,t.length-i);var c=this.compute_(e,t,s,a);return l&&c.unshift([S,l]),o&&c.push([S,o]),this.cleanupMerge(c),c},e.prototype.compute_=function(e,t,n,r){var a;if(!e)return[[j,t]];if(!t)return[[E,e]];var s=e.length>t.length?e:t,i=e.length>t.length?t:e,l=s.indexOf(i);if(-1!=l)return a=[[j,s.substring(0,l)],[S,i],[j,s.substring(l+i.length)]],e.length>t.length&&(a[0][0]=a[2][0]=E),a;if(1==i.length)return[[E,e],[j,t]];var o=this.halfMatch_(e,t);if(o){var c=o[0],h=o[1],u=o[2],g=o[3],f=o[4],p=this.main(c,u,n,r),d=this.main(h,g,n,r);return p.concat([[S,f]],d)}return n&&e.length>100&&t.length>100?this.lineMode_(e,t,r):this.bisect_(e,t,r)},e.prototype.lineMode_=function(e,t,n){e=(h=this.linesToChars_(e,t)).chars1,t=h.chars2;var r=h.lineArray,a=this.main(e,t,!1,n);this.charsToLines_(a,r),this.cleanupSemantic(a),a.push([S,""]);for(var s=0,i=0,l=0,o="",c="";s<a.length;){switch(a[s][0]){case j:l++,c+=a[s][1];break;case E:i++,o+=a[s][1];break;case S:if(i>=1&&l>=1){a.splice(s-i-l,i+l),s=s-i-l;for(var h,u=(h=this.main(o,c,!1,n)).length-1;u>=0;u--)a.splice(s,0,h[u]);s+=h.length}l=0,i=0,o="",c=""}s++}return a.pop(),a},e.prototype.bisect_=function(e,t,n){for(var r=e.length,a=t.length,s=Math.ceil((r+a)/2),i=s,l=2*s,o=new Array(l),c=new Array(l),h=0;h<l;h++)o[h]=-1,c[h]=-1;o[i+1]=0,c[i+1]=0;for(var u=r-a,g=u%2!=0,f=0,p=0,d=0,b=0,v=0;v<s&&!((new Date).getTime()>n);v++){for(var m=-v+f;m<=v-p;m+=2){for(var x=i+m,y=(M=m==-v||m!=v&&o[x-1]<o[x+1]?o[x+1]:o[x-1]+1)-m;M<r&&y<a&&e.charAt(M)==t.charAt(y);)M++,y++;if(o[x]=M,M>r)p+=2;else if(y>a)f+=2;else if(g&&(A=i+u-m)>=0&&A<l&&-1!=c[A]&&M>=(_=r-c[A]))return this.bisectSplit_(e,t,M,y,n)}for(var w=-v+d;w<=v-b;w+=2){for(var _,A=i+w,k=(_=w==-v||w!=v&&c[A-1]<c[A+1]?c[A+1]:c[A-1]+1)-w;_<r&&k<a&&e.charAt(r-_-1)==t.charAt(a-k-1);)_++,k++;if(c[A]=_,_>r)b+=2;else if(k>a)d+=2;else if(!g){var M;if((x=i+u-w)>=0&&x<l&&-1!=o[x]&&(y=i+(M=o[x])-x,M>=(_=r-_)))return this.bisectSplit_(e,t,M,y,n)}}}return[[E,e],[j,t]]},e.prototype.bisectSplit_=function(e,t,n,r,a){var s=e.substring(0,n),i=t.substring(0,r),l=e.substring(n),o=t.substring(r),c=this.main(s,i,!1,a),h=this.main(l,o,!1,a);return c.concat(h)},e.prototype.linesToChars_=function(e,t){var n=[],r={};function a(e){for(var t="",a=0,s=-1,i=n.length;s<e.length-1;){-1==(s=e.indexOf("\n",a))&&(s=e.length-1);var l=e.substring(a,s+1);a=s+1,(r.hasOwnProperty?r.hasOwnProperty(l):void 0!==r[l])?t+=String.fromCharCode(r[l]):(t+=String.fromCharCode(i),r[l]=i,n[i++]=l)}return t}return n[0]="",{chars1:a(e),chars2:a(t),lineArray:n}},e.prototype.charsToLines_=function(e,t){for(var n=0;n<e.length;n++){for(var r=e[n][1],a=[],s=0;s<r.length;s++)a[s]=t[r.charCodeAt(s)];e[n][1]=a.join("")}},e.prototype.commonPrefix=function(e,t){if(!e||!t||e.charAt(0)!=t.charAt(0))return 0;for(var n=0,r=Math.min(e.length,t.length),a=r,s=0;n<a;)e.substring(s,a)==t.substring(s,a)?s=n=a:r=a,a=Math.floor((r-n)/2+n);return a},e.prototype.commonSuffix=function(e,t){if(!e||!t||e.charAt(e.length-1)!=t.charAt(t.length-1))return 0;for(var n=0,r=Math.min(e.length,t.length),a=r,s=0;n<a;)e.substring(e.length-a,e.length-s)==t.substring(t.length-a,t.length-s)?s=n=a:r=a,a=Math.floor((r-n)/2+n);return a},e.prototype.commonOverlap_=function(e,t){var n=e.length,r=t.length;if(0==n||0==r)return 0;n>r?e=e.substring(n-r):n<r&&(t=t.substring(0,n));var a=Math.min(n,r);if(e==t)return a;for(var s=0,i=1;;){var l=e.substring(a-i),o=t.indexOf(l);if(-1==o)return s;i+=o,0!=o&&e.substring(a-i)!=t.substring(0,i)||(s=i,i++)}},e.prototype.halfMatch_=function(e,t){if(this.Timeout<=0)return null;var n=e.length>t.length?e:t,r=e.length>t.length?t:e;if(n.length<4||2*r.length<n.length)return null;var a=this;function s(e,t,n){for(var r,s,i,l,o=e.substring(n,n+Math.floor(e.length/4)),c=-1,h="";-1!=(c=t.indexOf(o,c+1));){var u=a.commonPrefix(e.substring(n),t.substring(c)),g=a.commonSuffix(e.substring(0,n),t.substring(0,c));h.length<g+u&&(h=t.substring(c-g,c)+t.substring(c,c+u),r=e.substring(0,n-g),s=e.substring(n+u),i=t.substring(0,c-g),l=t.substring(c+u))}return 2*h.length>=e.length?[r,s,i,l,h]:null}var i,l,o,c,h,u=s(n,r,Math.ceil(n.length/4)),g=s(n,r,Math.ceil(n.length/2));return u||g?(i=g?u&&u[4].length>g[4].length?u:g:u,e.length>t.length?(l=i[0],o=i[1],c=i[2],h=i[3]):(c=i[0],h=i[1],l=i[2],o=i[3]),[l,o,c,h,i[4]]):null},e.prototype.cleanupSemantic=function(e){for(var t=!1,n=[],r=0,a=null,s=0,i=0,l=0,o=0,c=0;s<e.length;)e[s][0]==S?(n[r++]=s,i=o,l=c,o=0,c=0,a=e[s][1]):(e[s][0]==j?o+=e[s][1].length:c+=e[s][1].length,a&&a.length<=Math.max(i,l)&&a.length<=Math.max(o,c)&&(e.splice(n[r-1],0,[E,a]),e[n[r-1]+1][0]=j,r--,s=--r>0?n[r-1]:-1,i=0,l=0,o=0,c=0,a=null,t=!0)),s++;for(t&&this.cleanupMerge(e),this.cleanupSemanticLossless(e),s=1;s<e.length;){if(e[s-1][0]==E&&e[s][0]==j){var h=e[s-1][1],u=e[s][1],g=this.commonOverlap_(h,u),f=this.commonOverlap_(u,h);g>=f?(g>=h.length/2||g>=u.length/2)&&(e.splice(s,0,[S,u.substring(0,g)]),e[s-1][1]=h.substring(0,h.length-g),e[s+1][1]=u.substring(g),s++):(f>=h.length/2||f>=u.length/2)&&(e.splice(s,0,[S,h.substring(0,f)]),e[s-1][0]=j,e[s-1][1]=u.substring(0,u.length-f),e[s+1][0]=E,e[s+1][1]=h.substring(f),s++),s++}s++}},e.prototype.cleanupSemanticLossless=function(t){function n(t,n){if(!t||!n)return 6;var r=t.charAt(t.length-1),a=n.charAt(0),s=r.match(e.nonAlphaNumericRegex_),i=a.match(e.nonAlphaNumericRegex_),l=s&&r.match(e.whitespaceRegex_),o=i&&a.match(e.whitespaceRegex_),c=l&&r.match(e.linebreakRegex_),h=o&&a.match(e.linebreakRegex_),u=c&&t.match(e.blanklineEndRegex_),g=h&&n.match(e.blanklineStartRegex_);return u||g?5:c||h?4:s&&!l&&o?3:l||o?2:s||i?1:0}for(var r=1;r<t.length-1;){if(t[r-1][0]==S&&t[r+1][0]==S){var a=t[r-1][1],s=t[r][1],i=t[r+1][1],l=this.commonSuffix(a,s);if(l){var o=s.substring(s.length-l);a=a.substring(0,a.length-l),s=o+s.substring(0,s.length-l),i=o+i}for(var c=a,h=s,u=i,g=n(a,s)+n(s,i);s.charAt(0)===i.charAt(0);){a+=s.charAt(0),s=s.substring(1)+i.charAt(0),i=i.substring(1);var f=n(a,s)+n(s,i);f>=g&&(g=f,c=a,h=s,u=i)}t[r-1][1]!=c&&(c?t[r-1][1]=c:(t.splice(r-1,1),r--),t[r][1]=h,u?t[r+1][1]=u:(t.splice(r+1,1),r--))}r++}},e.nonAlphaNumericRegex_=/[^a-zA-Z0-9]/,e.whitespaceRegex_=/\s/,e.linebreakRegex_=/[\r\n]/,e.blanklineEndRegex_=/\n\r?\n$/,e.blanklineStartRegex_=/^\r?\n\r?\n/,e.prototype.cleanupEfficiency=function(e){for(var t=!1,n=[],r=0,a=null,s=0,i=!1,l=!1,o=!1,c=!1;s<e.length;)e[s][0]==S?(e[s][1].length<this.EditCost&&(o||c)?(n[r++]=s,i=o,l=c,a=e[s][1]):(r=0,a=null),o=c=!1):(e[s][0]==E?c=!0:o=!0,a&&(i&&l&&o&&c||a.length<this.EditCost/2&&i+l+o+c==3)&&(e.splice(n[r-1],0,[E,a]),e[n[r-1]+1][0]=j,r--,a=null,i&&l?(o=c=!0,r=0):(s=--r>0?n[r-1]:-1,o=c=!1),t=!0)),s++;t&&this.cleanupMerge(e)},e.prototype.cleanupMerge=function(e){e.push([S,""]);for(var t,n=0,r=0,a=0,s="",i="";n<e.length;)switch(e[n][0]){case j:a++,i+=e[n][1],n++;break;case E:r++,s+=e[n][1],n++;break;case S:r+a>1?(0!==r&&0!==a&&(0!==(t=this.commonPrefix(i,s))&&(n-r-a>0&&e[n-r-a-1][0]==S?e[n-r-a-1][1]+=i.substring(0,t):(e.splice(0,0,[S,i.substring(0,t)]),n++),i=i.substring(t),s=s.substring(t)),0!==(t=this.commonSuffix(i,s))&&(e[n][1]=i.substring(i.length-t)+e[n][1],i=i.substring(0,i.length-t),s=s.substring(0,s.length-t))),0===r?e.splice(n-a,r+a,[j,i]):0===a?e.splice(n-r,r+a,[E,s]):e.splice(n-r-a,r+a,[E,s],[j,i]),n=n-r-a+(r?1:0)+(a?1:0)+1):0!==n&&e[n-1][0]==S?(e[n-1][1]+=e[n][1],e.splice(n,1)):n++,a=0,r=0,s="",i=""}""===e[e.length-1][1]&&e.pop();var l=!1;for(n=1;n<e.length-1;)e[n-1][0]==S&&e[n+1][0]==S&&(e[n][1].substring(e[n][1].length-e[n-1][1].length)==e[n-1][1]?(e[n][1]=e[n-1][1]+e[n][1].substring(0,e[n][1].length-e[n-1][1].length),e[n+1][1]=e[n-1][1]+e[n+1][1],e.splice(n-1,1),l=!0):e[n][1].substring(0,e[n+1][1].length)==e[n+1][1]&&(e[n-1][1]+=e[n+1][1],e[n][1]=e[n][1].substring(e[n+1][1].length)+e[n+1][1],e.splice(n+1,1),l=!0)),n++;l&&this.cleanupMerge(e)},e.prototype.xIndex=function(e,t){var n,r=0,a=0,s=0,i=0;for(n=0;n<e.length&&(e[n][0]!==j&&(r+=e[n][1].length),e[n][0]!==E&&(a+=e[n][1].length),!(r>t));n++)s=r,i=a;return e.length!=n&&e[n][0]===E?i:i+(t-s)},e.prototype.prettyHtml=function(e){for(var t=[],n=/&/g,r=/</g,a=/>/g,s=/\n/g,i=0;i<e.length;i++){var l=e[i][0],o=e[i][1].replace(n,"&amp;").replace(r,"&lt;").replace(a,"&gt;").replace(s,"<br/>");switch(l){case j:t[i]="<ins>"+o+"</ins>";break;case E:t[i]="<del>"+o+"</del>";break;case S:t[i]="<span>"+o+"</span>"}}return t.join("")},e.prototype.text1=function(e){for(var t=[],n=0;n<e.length;n++)e[n][0]!==j&&(t[n]=e[n][1]);return t.join("")},e.prototype.text2=function(e){for(var t=[],n=0;n<e.length;n++)e[n][0]!==E&&(t[n]=e[n][1]);return t.join("")},e.prototype.levenshtein=function(e){for(var t=0,n=0,r=0,a=0;a<e.length;a++){var s=e[a][0],i=e[a][1];switch(s){case j:n+=i.length;break;case E:r+=i.length;break;case S:t+=Math.max(n,r),n=0,r=0}}return t+Math.max(n,r)},e.prototype.toDelta=function(e){for(var t=[],n=0;n<e.length;n++)switch(e[n][0]){case j:t[n]="+"+encodeURI(e[n][1]);break;case E:t[n]="-"+e[n][1].length;break;case S:t[n]="="+e[n][1].length}return t.join("\t").replace(/%20/g," ")},e.prototype.fromDelta=function(e,t){for(var n=[],r=0,a=0,s=t.split(/\t/g),i=0;i<s.length;i++){var l=s[i].substring(1);switch(s[i].charAt(0)){case"+":try{n[r++]=[j,decodeURI(l)]}catch(h){throw new Error("Illegal escape in diff_fromDelta: "+l)}break;case"-":case"=":var o=parseInt(l,10);if(isNaN(o)||o<0)throw new Error("Invalid number in diff_fromDelta: "+l);var c=e.substring(a,a+=o);"="==s[i].charAt(0)?n[r++]=[S,c]:n[r++]=[E,c];break;default:if(s[i])throw new Error("Invalid diff operation in diff_fromDelta: "+s[i])}}if(a!=e.length)throw new Error("Delta length ("+a+") does not equal source text length ("+e.length+").");return n},e}());return r=a.main(e,t),a.cleanupSemantic(r),n=[],r.forEach((function(e){for(var t=e[0],r=e[1].split("\n"),a=0;a<r.length;a++)0===a?n.push([t,r[a]]):n.push([t,"\n".concat(r[a])])})),{textDiff:r=n}}(e,t='"'===(r=n=n.trim())[0]&&'"'===r[r.length-1]?r.slice(1,r.length-1):r);return T(a.textDiff)}const O=e=>(k("data-v-5ddfacbb"),e=e(),M(),e),D={class:"bg-[#f3f8fa] p-2 h-full overflow-auto"},H={class:"flex h-[220px]"},I={class:"p-4 flex-1 bg-[#fff] rounded-md"},L=O((()=>s("div",{class:"text-[16px] font-bold mb-2 text-gray-600"}," 请在此输入或粘贴英文 ",-1))),N={class:"my-6 items-center"},F={class:"mb-5"},P=["onClick"],U=O((()=>s("div",{class:"text-[16px] font-bold text-gray-600 mr-6"},"AI润色结果",-1))),V={key:0},z={class:"p-4 bg-white rounded-md mb-4"},q=m('<div class="flex justify-between items-center mb-3" data-v-5ddfacbb><div class="text-[16px] font-bold text-[#3A7EC3]" data-v-5ddfacbb>润色分析</div><div class="flex items-center" data-v-5ddfacbb><span class="text-gray-500 mr-2" data-v-5ddfacbb>注解：</span><div class="flex items-center" data-v-5ddfacbb><div class="flex items-center mr-4" data-v-5ddfacbb><div class="rounded-1/2 w-3 h-3 bg-[rgba(0,145,255,.2)] mr-2" data-v-5ddfacbb></div><del class="bg-[rgba(0,145,255,.2)] text-[#0091ff] font-bold px-2" data-v-5ddfacbb>delete</del></div><div class="flex items-center" data-v-5ddfacbb><div class="rounded-1/2 w-3 h-3 bg-[rgba(255,1,1,.2)] mr-2" data-v-5ddfacbb></div><ins class="bg-[rgba(255,1,1,.2)] text-[#ff0101] font-bold px-2" data-v-5ddfacbb>insert</ins></div></div></div></div>',1),B=["innerHTML"],Q={class:"p-4 bg-white rounded-md"},W={class:"mb-3 flex justify-between items-center"},X=O((()=>s("span",{class:"text-[16px] font-bold text-[#3A7EC3]"},"润色结果",-1))),Z=O((()=>s("div",{class:"text-[16px] font-bold text-[#3A7EC3] mb-2"}," 润色翻译 ",-1))),$=e({__name:"index",setup(e){const m=t(""),k=t(null),M=t(null),E=t([{label:"经典学术写作风格",value:.6},{label:"简洁写作风格",value:.9}]),j=()=>{},S=()=>{if(e=m.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(e)||!m.value)return x.warning("请输入英文内容");var e;C("polish",{text:m.value,temperature:k.value,isTrans:1}).then((e=>{M.value=e.data.result,M.value.analysis=R(M.value.text,M.value.polish),T()}))},T=()=>{C("polish",{text:"r",tr:y(M.value.polish).split("").reverse().join(""),temperature:k.value,isTrans:0,remain:1}).then((e=>{M.value.polishTrans=e.data}))};return(e,t)=>{var x;const y=w,C=_,T=A,R=n("copy");return r(),a("div",D,[s("div",H,[s("div",I,[L,i(y,{modelValue:l(m),"onUpdate:modelValue":t[0]||(t[0]=e=>o(m)?m.value=e:null),rows:8,type:"textarea",placeholder:"请输入内容","show-word-limit":"",resize:"none",maxlength:600,onInput:j},null,8,["modelValue"])]),c("",!0)]),s("div",null,[s("div",N,[s("div",F,[(r(!0),a(h,null,u(l(E),((e,t)=>(r(),a("div",{key:t,class:g(["px-4 py-2 text-white text-[12px] font-bold rounded cursor-pointer mr-2 tool-bar-btn inline-block",l(k)==e.value?"bg-[#409eff]":"bg-gray-400"]),onClick:t=>(e=>{k.value=e.value,S()})(e)},f(e.label),11,P)))),128))]),U]),l(M)?(r(),a("div",V,[s("div",z,[q,s("div",{class:"leading-[22px]",innerHTML:l(M).analysis},null,8,B)]),s("div",Q,[s("div",null,[s("div",W,[X,p((r(),d(C,{class:"cursor-pointer"},{default:b((()=>[i(l(v))])),_:1})),[[R,l(E)[0].label]])]),s("div",null,f(null==(x=l(M))?void 0:x.polish),1)]),i(T,{class:"bg-[#eaf2f7] !border-top-0"}),s("div",null,[Z,s("div",null,f(l(M).polishTrans),1)])])])):c("",!0)])])}}},[["__scopeId","data-v-5ddfacbb"]]);export{$ as default};
