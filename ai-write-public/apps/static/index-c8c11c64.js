/* empty css                  *//* empty css                 */import{s as e}from"./index-4b27c7f5.js";import{r as a,aK as l,d as s,e as t,f as u,g as r,q as i,j as n,m as o,k as v,F as m,z as d,V as p,h as c,aM as f,i as x,a3 as y,C as j,E as h,H as g}from"./index-b7023353.js";import{s as k}from"./index-2adb375f.js";/* empty css                   */const w={class:"pt-10 overflow-auto h-full"},F={class:"flex justify-center my-10"},_={key:0},V=["innerHTML"],b={class:"flex justify-center mt-10"},z={__name:"index",setup(z){const A=a(""),C=a([]),E=a([]),H=a(5),M=()=>{if(a=A.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(a)||!A.value)return y.warning("请输入英文内容");var a;e({text:A.value}).then((e=>{e&&e&&e.data&&(C.value=e.data,C.value=k(C.value),H.value=5,S())}))},S=()=>{let e=[];5==H.value?(e=[0,5],H.value=3):3==H.value?(e=[5,8],H.value=2):2==H.value&&(e=[8,10],H.value=5),E.value=JSON.parse(JSON.stringify(C.value)).slice(e[0],e[1])};return(e,a)=>{const y=j,k=h,z=g,C=l("copy");return s(),t("div",w,[u(y,{modelValue:r(A),"onUpdate:modelValue":a[0]||(a[0]=e=>i(A)?A.value=e:null),rows:8,type:"textarea",placeholder:"请输入不超过250单词的段落","show-word-limit":"",resize:"none",maxlength:250},null,8,["modelValue"]),n("div",F,[u(k,{type:"primary",onClick:M},{default:o((()=>a[1]||(a[1]=[v("改 写")]))),_:1})]),r(E)&&r(E).length?(s(),t("div",_,[(s(!0),t(m,null,d(r(E),((e,a)=>(s(),t("div",{class:"flex justify-center mb-6 relative",key:a},[n("span",{innerHTML:e.textShow,class:"text-[18px]"},null,8,V),p((s(),c(z,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-2 absolute"},{default:o((()=>[u(r(f))])),_:2},1024)),[[C,e.text]])])))),128)),n("div",b,[u(k,{type:"primary",link:"",onClick:S},{default:o((()=>a[2]||(a[2]=[v("换一换")]))),_:1})])])):x("",!0)])}}};export{z as default};
