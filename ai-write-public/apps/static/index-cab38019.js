/* empty css                  *//* empty css                     *//* empty css                 */import{b as e,a as o,u as s,r as l,O as t,y as n,o as a,az as i,d as c,e as r,j as d,t as u,f as p,m,k as g,g as f,aA as I,aB as _,C as v,aC as k,E as h,aD as B,aE as b}from"./index-b7023353.js";import{_ as y}from"./_plugin-vue_export-helper-1b428a4d.js";const x={class:"bg-background text-foreground antialiased min-h-screen flex items-center justify-center"},w={class:"cl-rootBox cl-signUp-root justify-center"},S={class:"cl-cardBox cl-signUp-start"},T={class:"cl-card cl-signUp-start"},U={class:"cl-header"},j={class:"cl-headerTitle"},$={class:"cl-headerSubtitle"},A={class:"cl-main"},O={class:"cl-socialButtonsRoot"},C={class:"cl-socialButtons"},z={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},J={class:"cl-socialButtonsBlockButton-d"},N={class:"cl-socialButtons"},q={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},V={class:"cl-socialButtonsBlockButton-d"},L={class:"cl-dividerRow"},P={class:"cl-dividerText"},R={class:"cl-socialButtonsRoot"},E={class:"cl-internal-1pnppin"},G={class:"cl-internal-742eeh"},M={class:"cl-internal-2iusy0"},W={class:"cl-footer cl-internal-4x6jej"},Z={class:"cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70"},F={class:"cl-footerActionText cl-internal-kyvqj0","data-localization-key":"signUp.start.actionText"},H=y({__name:"index",setup(y){var H;const{t:D}=e(),Q=o(),K=s(),X=Q.params.socialType,Y=Q.query.authCode,ee=Q.query.authState,oe=l({email:"",password:""}),se=(null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location?void 0:location.origin.includes("medsci.cn")),le=l(),te=t({password:[{required:!0,message:D("tool.password_cannot_be_empty"),trigger:"blur"},{min:8,max:20,message:D("tool.pwdLength"),trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,message:D("tool.password_includes_uppercase_lowercase_numbers_and_symbols"),trigger:"blur"}],email:[{required:!0,message:D("tool.email_address_cannot_be_empty"),trigger:"blur"},{type:"email",message:D("tool.please_enter_a_valid_email_address"),trigger:"blur"}]}),ne=n.get("userInfo")?null==(H=JSON.parse(n.get("userInfo")))?void 0:H.userId:"",ae=e=>{I(e).then((e=>{window.location.href=e}))};return a((()=>{ne?K.push("/"):X&&Y&&ee&&i(X,Y,ee).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?n.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?n.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):n.set("userInfo",JSON.stringify(e.userInfo),{expires:365}),K.push("/"))}))})),(e,o)=>{const s=v,l=k,t=h,a=B,i=b;return c(),r("div",x,[d("div",w,[d("div",S,[d("div",T,[d("div",U,[d("div",null,[d("h1",j,u(e.$t("tool.login_to_MedSci_xAI")),1),d("p",$,u(e.$t("tool.welcome_back_please_login_to_continue")),1)])]),d("div",A,[d("div",O,[d("div",C,[d("button",z,[d("span",J,[o[5]||(o[5]=d("span",null,[d("img",{src:"https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1)),d("span",{class:"cl-socialButtonsBlockButtonText",onClick:o[0]||(o[0]=e=>ae(35))},u(e.$t("tool.continue_with_google")),1)])])]),d("div",N,[d("button",q,[d("span",V,[o[6]||(o[6]=d("span",null,[d("img",{src:"https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1)),d("span",{class:"cl-socialButtonsBlockButtonText",onClick:o[1]||(o[1]=e=>ae(36))},u(e.$t("tool.continue_with_facebook")),1)])])])]),d("div",L,[o[7]||(o[7]=d("div",{class:"cl-dividerLine"},null,-1)),d("p",P,u(e.$t("tool.or")),1),o[8]||(o[8]=d("div",{class:"cl-dividerLine"},null,-1))]),d("div",R,[p(a,{ref_key:"ruleFormRef",ref:le,style:{"max-width":"600px"},model:oe.value,rules:te,"label-width":"auto",class:"demo-ruleForm","label-position":"left",size:e.formSize,"status-icon":""},{default:m((()=>[p(l,{label:e.$t("tool.email"),prop:"email"},{default:m((()=>[p(s,{modelValue:oe.value.email,"onUpdate:modelValue":o[2]||(o[2]=e=>oe.value.email=e)},null,8,["modelValue"])])),_:1},8,["label"]),p(l,{label:e.$t("tool.password"),prop:"password",style:{"padding-bottom":"20px"}},{default:m((()=>[p(s,{modelValue:oe.value.password,"onUpdate:modelValue":o[3]||(o[3]=e=>oe.value.password=e),"show-password":"true"},null,8,["modelValue"])])),_:1},8,["label"]),p(l,null,{default:m((()=>[d("div",E,[o[10]||(o[10]=d("div",{id:"clerk-captcha",class:"cl-internal-3s7k9k"},null,-1)),d("div",G,[p(t,{class:"cl-formButtonPrimary cl-button cl-internal-ttumny",onClick:o[4]||(o[4]=e=>(async e=>{e&&await e.validate(((e,o)=>{e&&_(oe.value).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?n.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?n.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):n.set("userInfo",JSON.stringify(e.userInfo),{expires:365}),K.push("/"))}))}))})(le.value))},{default:m((()=>[d("span",M,[g(u(e.$t("tool.continue")),1),o[9]||(o[9]=d("svg",{class:"cl-buttonArrowIcon cl-internal-1c4ikgf"},[d("path",{fill:"currentColor",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"m7.25 5-3.5-2.25v4.5L7.25 5Z"})],-1))])])),_:1})])])])),_:1})])),_:1},8,["model","rules","size"])])])]),d("div",W,[d("div",Z,[d("span",F,u(e.$t("tool.no_account_yet")),1),p(i,{href:f(se)?"/apps/sign-up":"/sign-up",class:"cl-footerActionLink"},{default:m((()=>[g(u(e.$t("tool.signUp")),1)])),_:1},8,["href"])])])])])])}}},[["__scopeId","data-v-8d380b4c"]]);export{H as default};
