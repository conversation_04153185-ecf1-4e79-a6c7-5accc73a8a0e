/* empty css                  *//* empty css                *//* empty css                 */import{s as e}from"./index-7f4caa86.js";import{r as a,aQ as l,d as s,e as t,f as u,g as i,p as r,j as n,m as o,k as m,F as p,A as v,V as d,h as c,aR as f,i as x,a7 as y,D as j,E as h,I as g}from"./index-837d36db.js";import{s as k}from"./index-5fc9c050.js";/* empty css                   */const w={class:"pt-10 overflow-auto h-full"},F={class:"flex justify-center my-10"},_={key:0},A=["innerHTML"],V={class:"flex justify-center mt-10"},b={__name:"index",setup(b){const E=a(""),S=a([]),z=a([]),C=a(5),H=()=>{if(a=E.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(a)||!E.value)return y.warning("请输入英文内容");var a;e({text:E.value}).then((e=>{e&&e&&e.data&&(S.value=e.data,S.value=k(S.value),C.value=5,J())}))},J=()=>{let e=[];5==C.value?(e=[0,5],C.value=3):3==C.value?(e=[5,8],C.value=2):2==C.value&&(e=[8,10],C.value=5),z.value=JSON.parse(JSON.stringify(S.value)).slice(e[0],e[1])};return(e,a)=>{const y=j,k=h,b=g,S=l("copy");return s(),t("div",w,[u(y,{modelValue:i(E),"onUpdate:modelValue":a[0]||(a[0]=e=>r(E)?E.value=e:null),rows:8,type:"textarea",placeholder:"请输入不超过250单词的段落","show-word-limit":"",resize:"none",maxlength:250},null,8,["modelValue"]),n("div",F,[u(k,{type:"primary",onClick:H},{default:o((()=>[m("改 写")])),_:1})]),i(z)&&i(z).length?(s(),t("div",_,[(s(!0),t(p,null,v(i(z),((e,a)=>(s(),t("div",{class:"flex justify-center mb-6 relative",key:a},[n("span",{innerHTML:e.textShow,class:"text-[18px]"},null,8,A),d((s(),c(b,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-2 absolute"},{default:o((()=>[u(i(f))])),_:2},1024)),[[S,e.text]])])))),128)),n("div",V,[u(k,{type:"primary",link:"",onClick:J},{default:o((()=>[m("换一换")])),_:1})])])):x("",!0)])}}};export{b as default};
