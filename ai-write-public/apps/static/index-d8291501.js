import{M as a,u as s,o as e,a3 as n,al as i,c as o,d as t}from"./index-46a7add2.js";const r=a({__name:"index",setup(a){const r=s(),c=JSON.parse(decodeURIComponent(r.params.payInfo));return e((async()=>{const a=navigator.userAgent;if(null!=a)if(a.includes("MicroMessenger"))n.warning("请打开支付宝扫码");else if(a.includes("AlipayClient")){const a=await i(c);location.href=a}})),(a,s)=>(o(),t("div"))}});export{r as default};
