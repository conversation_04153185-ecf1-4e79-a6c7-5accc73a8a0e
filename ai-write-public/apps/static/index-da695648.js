import{a as e,z as a,aA as s,$ as o,aB as n,d as t,e as l,j as i,t as r,i as c,F as d,A as u,ai as m,a4 as h,x as g,y as p}from"./index-51e6c9ce.js";const v={data:()=>({avatar:"",userInfo:null,isIncludeTool:!1,selectedLanguage:"",langs:[],hrefUrl:""}),watch:{userInfoCookie(e,a){}},computed:{baseUrl:()=>"https://www.medsci.cn/",userInfoCookie:()=>a.get("userInfo")},methods:{changeImg(){this.avatar="https://img.medsci.cn/web/img/user_icon.png"},getCookieUserInfo:()=>a.get("userInfo"),showMenu(){this.$refs.menu.style.display="block"},hideMenu(){this.$refs.menu.style.display="none"},showMenu1(){this.$refs.menu1.style.display="block"},hideMenu1(){this.$refs.menu1.style.display="none"},toggle(e){const a=this.$route.path,s=this.$route.params.lang;if(s){const o=a.replace(`/${s}`,"");this.$router.push(`/${e}${o}`)}else this.$router.push(`/${e}${a}`);window.localStorage.setItem("ai_apps_lang",e),this.selectedLanguage=e,this.$i18n.locale=e,this.$emit("getAppLang",e),"zh-CN"==localStorage.getItem("ai_apps_lang")?this.$emit("isZHChange",!0):this.$emit("isZHChange",!1),this.$refs.menu.style.display="none"},async logout(){a.remove("userInfo",{domain:".medon.com.cn"}),a.remove("userInfo",{domain:".medsci.cn"}),a.remove("userInfo",{domain:"localhost"}),localStorage.removeItem("conversation"),localStorage.removeItem("writeContent");let e=localStorage.getItem("socialType");e&&35==e||e&&36==e?s().then((()=>{localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),location.reload()})).catch((e=>{})):(localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),window.location.origin.includes("medsci.cn")?window.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.location.href:window.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.location.href)},async loginAccount(){let e=o();e&&"zh-CN"!=e?this.$router.push("/login"):window.addLoginDom()},medsciSearch(){let e=document.getElementById("medsciSearchKeyword").value;e?window.open(`https://www.medsci.cn/search?q=${e}`,"_blank"):window.open("https://search.medsci.cn/","_blank")},getAppLangsData(){n().then((e=>{e.map((e=>{e.status||this.langs.push({name:e.value,value:e.remark})}))}))}},mounted(){var e,s;this.hrefUrl="https://ai.medsci.cn",this.getAppLangsData(),this.userInfo=a.get("userInfo")?JSON.parse(a.get("userInfo")):null,this.isIncludeTool=window.location.href.includes("/tool"),setTimeout((()=>{this.selectedLanguage=o()}),0),this.avatar=(null==(e=this.userInfo)?void 0:e.avatar)?null==(s=this.userInfo)?void 0:s.avatar:"https://img.medsci.cn/web/img/user_icon.png"}},w=e=>(g("data-v-bcda31aa"),e=e(),p(),e),I={class:"header ms-header-media"},f={class:"ms-header"},y={class:"wrapper"},k={class:"main-menu-placeholder wrapper clearfix",style:{height:"56px",display:"block !important"}},$={class:"ms-header-img"},S=["href"],M=[w((()=>i("img",{src:"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:""},null,-1)))],_={id:"main-menu",class:"ms-header-nav"},b={class:"header-top header-user",id:"user-info-header"},C={key:0,class:"change_lang"},T={class:"current_lang"},x={class:"ms-link"},U={class:"ms-dropdown-menu",ref:"menu"},L={class:"new-header-avator-pop",id:"new-header-avator"},A={class:"new-header-bottom",style:{padding:"0"}},N={class:"langUl"},j=["onClick"],z={class:"index-user-img_right"},D=w((()=>i("li",null,null,-1))),E={href:"#"},B={class:"img-area"},H=["src"],Z={class:"ms-dropdown-menu",ref:"menu1"},q={class:"new-header-avator-pop",id:"new-header-avator"},F={class:"new-header-top"},G={class:"new-header-info"},J=["src"],K={class:"new-header-name"};const O=e(v,[["render",function(e,a,s,o,n,g){var p,v,w,O,P;return t(),l("div",I,[i("div",f,[i("div",y,[i("div",k,[i("div",$,[i("a",{href:n.hrefUrl},M,8,S)]),i("div",_,[i("div",b,[i("ul",null,[i("li",{class:"index-user-img index-user-img_left",onMouseover:a[0]||(a[0]=(...e)=>g.showMenu&&g.showMenu(...e)),onMouseout:a[1]||(a[1]=(...e)=>g.hideMenu&&g.hideMenu(...e)),onClick:a[2]||(a[2]=(...e)=>g.showMenu&&g.showMenu(...e))},[n.isIncludeTool?c("",!0):(t(),l("div",C,[i("span",T,r(null==(p=n.langs.filter((e=>e.value==n.selectedLanguage))[0])?void 0:p.name),1),i("span",x,r(e.$t("market.switch")),1)])),i("div",U,[i("div",L,[i("div",A,[i("div",N,[(t(!0),l(d,null,u(n.langs,(e=>(t(),l("p",{key:e,onClick:m((a=>g.toggle(e.value)),["stop"]),class:h({langItemSelected:e.value===n.selectedLanguage})},r(e.name),11,j)))),128))])])])],512)],32),(null==(v=n.userInfo)?void 0:v.userId)?(t(),l("li",{key:1,class:"index-user-img",onMouseover:a[7]||(a[7]=(...e)=>g.showMenu1&&g.showMenu1(...e)),onMouseout:a[8]||(a[8]=(...e)=>g.hideMenu1&&g.hideMenu1(...e))},[i("a",E,[i("div",B,[i("img",{src:n.avatar,onError:a[4]||(a[4]=(...e)=>g.changeImg&&g.changeImg(...e)),alt:""},null,40,H)])]),i("div",Z,[i("div",q,[i("a",{class:"new-header-exit ms-statis","ms-statis":"logout",href:"#",onClick:a[5]||(a[5]=e=>g.logout())},r(e.$t("market.logout")),1),i("div",F,[i("div",G,[i("img",{class:"new-header-avatar",src:n.avatar,onError:a[6]||(a[6]=(...e)=>g.changeImg&&g.changeImg(...e)),alt:""},null,40,J),i("div",K,[i("span",null,r((null==(w=n.userInfo)?void 0:w.realName)?null==(O=n.userInfo)?void 0:O.realName:null==(P=n.userInfo)?void 0:P.userName),1)])])])])],512)],32)):(t(),l(d,{key:0},[i("li",z,[i("a",{href:"javascript: void(0)",class:"ms-link",onClick:a[3]||(a[3]=(...e)=>g.loginAccount&&g.loginAccount(...e))},r(e.$t("market.login")),1)]),D],64))])])])])])])])}],["__scopeId","data-v-bcda31aa"]]);export{O as _};
