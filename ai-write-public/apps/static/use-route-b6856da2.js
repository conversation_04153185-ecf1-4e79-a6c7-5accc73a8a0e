import{I as e}from"https://static.medsci.cn/ai-write/static/index-6e66f8c2.js";function t(e){let t,n,o,r=!1;return function(a){void 0===t?(t=a,n=0,o=-1):t=function(e,t){const n=new Uint8Array(e.length+t.length);return n.set(e),n.set(t,e.length),n}(t,a);const c=t.length;let i=0;for(;n<c;){r&&(10===t[n]&&(i=++n),r=!1);let a=-1;for(;n<c&&-1===a;++n)switch(t[n]){case 58:-1===o&&(o=n-i);break;case 13:r=!0;case 10:a=n}if(-1===a)break;e(t.subarray(i,a),o),i=n,o=-1}i===c?t=void 0:0!==i&&(t=t.subarray(i),n-=i)}}var n=globalThis&&globalThis.__rest||function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const o="text/event-stream",r="last-event-id";function a(e,a){var{signal:i,headers:s,onopen:l,onmessage:d,onclose:u,onerror:f,openWhenHidden:b,fetch:h}=a,p=n(a,["signal","headers","onopen","onmessage","onclose","onerror","openWhenHidden","fetch"]);return new Promise(((n,a)=>{const y=Object.assign({},s);let g;function v(){g.abort(),document.hidden||E()}y.accept||(y.accept=o),b||document.addEventListener("visibilitychange",v);let w=1e3,m=0;function O(){document.removeEventListener("visibilitychange",v),window.clearTimeout(m),g.abort()}null==i||i.addEventListener("abort",(()=>{O(),n()}));const j=null!=h?h:window.fetch,x=null!=l?l:c;async function E(){var o;g=new AbortController;try{const o=await j(e,Object.assign(Object.assign({},p),{headers:y,signal:g.signal}));await x(o),await async function(e,t){const n=e.getReader();let o;for(;!(o=await n.read()).done;)t(o.value)}(o.body,t(function(e,t,n){let o={data:"",event:"",id:"",retry:void 0};const r=new TextDecoder;return function(a,c){if(0===a.length)null==n||n(o),o={data:"",event:"",id:"",retry:void 0};else if(c>0){const n=r.decode(a.subarray(0,c)),i=c+(32===a[c+1]?2:1),s=r.decode(a.subarray(i));switch(n){case"data":o.data=o.data?o.data+"\n"+s:s;break;case"event":o.event=s;break;case"id":e(o.id=s);break;case"retry":const n=parseInt(s,10);isNaN(n)||t(o.retry=n)}}}}((e=>{e?y[r]=e:delete y[r]}),(e=>{w=e}),d))),null==u||u(),O(),n()}catch(c){if(!g.signal.aborted)try{const e=null!==(o=null==f?void 0:f(c))&&void 0!==o?o:w;window.clearTimeout(m),m=window.setTimeout(E,e)}catch(i){O(),a(i)}}}E()}))}function c(e){const t=e.headers.get("content-type");if(!(null==t?void 0:t.startsWith(o)))throw new Error(`Expected content-type to be ${o}, Actual: ${t}`)}const i={to:[String,Object],url:String,replace:Boolean};function s({to:e,url:t,replace:n,$router:o}){e&&o?o[n?"replace":"push"](e):t&&(n?location.replace(t):location.href=t)}function l(){const t=e().proxy;return()=>s(t)}export{i as a,a as f,s as r,l as u};
