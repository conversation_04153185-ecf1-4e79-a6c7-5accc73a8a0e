// middleware/unknownRoute.global.ts

export default defineNuxtRouteMiddleware((to, from) => {
    // 匹配以 /zh 开头的所有路径
    if (to.path.startsWith('/zh')) {
      // 去掉前缀 /zh
      const newPath = to.path.replace(/^\/zh/, '');

      // 如果 newPath 是空路径（如：/zh/），默认跳转到根路径 /
      const finalPath = newPath === '' ? '/' : newPath;

      return navigateTo(finalPath, { redirectCode: 301 });
    }
    // 如果当前路由没有匹配到任何页面
    if (!to.matched.length) {
        console.log('当前路由没有匹配到任何页面')
      // 重定向到首页
      // return navigateTo('/')
    }
  })
